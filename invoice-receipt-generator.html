<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发票/收据生成器 - SmartOffice 2.0</title>
    
    <!-- 外部依赖库 - 带错误处理 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"
            onerror="console.warn('html2canvas CDN加载失败，PDF导出功能可能受限')"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"
            onerror="console.warn('jsPDF CDN加载失败，PDF导出功能可能受限')"></script>

    <!-- 外部图片资源管理模块 - 延迟加载确保依赖顺序 -->
    <script src="images-logo.js" defer onerror="console.warn('标志图片管理模块加载失败')"></script>
    <script src="images-header.js" defer onerror="console.warn('页眉图片管理模块加载失败')"></script>
    <script src="images-footer.js" defer onerror="console.warn('页脚图片管理模块加载失败')"></script>
    <script src="images-stamp.js" defer onerror="console.warn('印章图片管理模块加载失败')"></script>

    <!-- 导出组件模块 - 延迟加载确保主文件先初始化 -->
    <script src="export-components.js" defer onerror="console.warn('导出组件模块加载失败')"></script>
    
    <style>
        /**
         * @file 发票/收据生成器样式
         * @description 整合的发票收据专用样式文件
         */
        
        /* #region CSS变量定义 */
        :root {
            /* A4纸张标准尺寸 */
            --a4-width-px: 794px;
            --a4-height-px: 1123px;
            
            /* 布局变量 */
            --header-height: 120px;
            --footer-height: 120px;
            --content-padding: 20px;
            
            /* 页边距 */
            --margin-top-px: 100px;
            --margin-bottom-px: 80px;
            --margin-left-px: 37.8px;
            --margin-right-px: 37.8px;
            
            /* 颜色变量 */
            --primary-color: #1e40af;
            --secondary-color: #3b82f6;
            --accent-color: #f59e0b;
            --light-color: #f3f4f6;
            --dark-color: #1f2937;
            --background-color: #f9fafb;
            --text-color: #333333;
            --border-color: #e5e7eb;
            
            /* 字体变量 */
            --base-font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            --classic-font-family: 'Times New Roman', 'SimSun', serif;
            --elegant-font-family: 'Georgia', 'STZhongsong', serif;
            --base-font-size: 11pt;
            --title-font-size: 18pt;
            --small-font-size: 9pt;
            --line-height: 1.5;
            
            /* 阴影变量 */
            --box-shadow-light: 0 2px 4px rgba(0,0,0,0.1);
            --box-shadow-medium: 0 4px 15px -1px rgba(0, 0, 0, 0.1), 0 6px 8px -1px rgba(0, 0, 0, 0.05);
            
            /* 过渡变量 */
            --transition-fast: 0.15s ease;
            --transition-normal: 0.3s ease;
            
            /* Z-index层级 - 修复重叠问题 */
            --z-index-header: 100;
            --z-index-footer: 100;
            --z-index-stamp: 30;        /* 降低印章层级，避免遮挡总金额 */
            --z-index-total: 200;       /* 总金额最高层级 */
            
            /* 预览容器缩放比例 */
            --preview-scale-factor: 0.75;
            
            /* 印章定位 - 优化位置避免与总金额重叠 */
            --stamp-bottom-offset: 15%;  /* 降低底部偏移，避免与总金额重叠 */
            --stamp-right-offset: 5%;    /* 减少右侧偏移，给总金额留出空间 */
        }
        /* #endregion */
        
        /* #region 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--base-font-family);
            font-size: var(--base-font-size);
            line-height: var(--line-height);
            color: var(--text-color);
            background-color: var(--background-color);
        }
        
        /* 隐藏类 */
        .hidden {
            display: none !important;
        }
        /* #endregion */
        
        /* #region 布局样式 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: start;
        }
        
        .form-section {
            background: white;
            border-radius: 8px;
            box-shadow: var(--box-shadow-medium);
            padding: 25px;
        }
        
        .preview-section {
            background: white;
            border-radius: 8px;
            box-shadow: var(--box-shadow-medium);
            padding: 25px;
        }
        /* #endregion */
        
        /* #region 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            transition: var(--transition-fast);
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .items-table th,
        .items-table td {
            padding: 8px;
            border: 1px solid var(--border-color);
            text-align: left;
        }
        
        .items-table th {
            background-color: var(--light-color);
            font-weight: 600;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-fast);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #1e3a8a;
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #2563eb;
        }
        
        .btn-success {
            background-color: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #059669;
        }
        
        .btn-danger {
            background-color: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #dc2626;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        /* #endregion */
        
        /* #region A4纸张和文档容器样式 */
        .a4-page {
            width: var(--a4-width-px);
            min-height: var(--a4-height-px);
            background: white;
            margin: 0 auto;
            box-shadow: var(--box-shadow-light);
            position: relative;
            overflow: hidden;
        }
        
        #document-preview {
            /* 严格A4尺寸设置 - 修复尺寸显示问题 */
            width: var(--a4-width-px) !important;
            height: var(--a4-height-px) !important;
            min-width: var(--a4-width-px);
            min-height: var(--a4-height-px);
            max-width: var(--a4-width-px);
            max-height: var(--a4-height-px);
            margin: 0 auto 30px;
            padding: 0;
            background-color: white;
            box-shadow: var(--box-shadow-medium);
            transform: scale(var(--preview-scale-factor));
            transform-origin: top center;
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden; /* 防止内容溢出A4边界 */
            /* 强制A4比例 - 210:297 = 1:1.414 */
            aspect-ratio: 210 / 297;
            /* 确保容器独立性 */
            display: block;
            flex-shrink: 0;
        }
        
        #document-container {
            position: relative;
            width: var(--a4-width-px);
            height: var(--a4-height-px);
            min-height: var(--a4-height-px);
            background: white;
            margin: 0;
            /* 优化内容区域padding - 页眉改为相对定位后调整 */
            padding-top: 20px; /* 减少顶部padding，因为页眉不再绝对定位 */
            padding-bottom: calc(var(--footer-height) + 15px);
            padding-left: calc(var(--content-padding) + 10px); /* 左边距增加10px */
            padding-right: calc(var(--content-padding) + 10px); /* 右边距增加10px */
            display: flex;
            flex-direction: column;
            font-size: var(--base-font-size);
            line-height: var(--line-height);
            color: var(--text-color);
            overflow: hidden; /* 防止内容溢出A4边界 */
            transform-origin: top center;
            /* 确保内容在A4范围内 */
            box-sizing: border-box;
        }
        
        #preview-container {
            background: #f5f5f5;
            padding: 20px;
            /* 精确计算容器尺寸以适应缩放后的A4 */
            min-height: calc(var(--a4-height-px) * var(--preview-scale-factor) + 80px);
            min-width: calc(var(--a4-width-px) * var(--preview-scale-factor) + 60px);
            /* 确保容器能完整显示A4预览 */
            width: 100%;
            max-width: 100%;
            overflow: auto;
            border-radius: 6px;
            /* 确保预览居中显示 */
            display: flex;
            flex-direction: column;
            align-items: center;            /* 添加A4预览指示 */
            position: relative;
        }

        /* A4预览容器增强 */
        #preview-container::before {
            content: "A4 预览 (794×1123px) / A4 Preview";
            position: absolute;
            top: 5px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 11px;
            color: #666;
            background: rgba(255, 255, 255, 0.9);
            padding: 2px 8px;
            border-radius: 3px;
            z-index: 1000;
        }

        /* A4预览精确度验证 */
        #document-preview::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 1px dashed rgba(30, 64, 175, 0.3);
            pointer-events: none;
            z-index: 999;
        }

        /* 内容溢出警告 */
        .content-overflow-warning {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(239, 68, 68, 0.9);
            color: rgba(255, 255, 255, 0);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1001;
            display: none;
        }
        /* #endregion */
        
        /* #region 页眉页脚样式 */
        .document-header,
        .document-header-image-container {
            position: relative; /* 改为相对定位，避免覆盖内容 */
            width: 100%;
            height: var(--header-height);
            background: white; /* 修复：使用纯白色背景，避免透明度问题 */
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: none; /* 修复：移除阴影，避免导出时出现灰色边框 */
            padding: 5px;
            /* 确保页眉在A4边界内 */
            box-sizing: border-box;
            margin: 0 auto 15px auto; /* 添加底部间距 */
            /* 确保页眉图片不会超出容器 */
            overflow: hidden;
        }
        
        .document-header-image-container img {
            /* 完全填充显示 - 固定高度填充策略 */
            height: 100% !important; /* 完全填充90px高度的页眉容器 */
            width: 100% !important; /* 完全填充容器宽度 */
            max-width: none !important; /* 移除最大宽度限制 */
            object-fit: cover !important; /* 完全填充容器，保持比例，可能裁剪 */
            object-position: center !important; /* 居中对齐，确保重要部分可见 */
            display: block !important;
            margin: 0 !important; /* 移除边距，确保贴合容器边界 */
            /* 无损高质量渲染 - 兼容多浏览器 */
            image-rendering: -webkit-optimize-contrast !important; /* Safari/Chrome */
            image-rendering: -webkit-optimize-contrast !important; /* Edge 79+ */
            image-rendering: crisp-edges !important; /* Firefox/Chrome */
            image-rendering: high-quality !important; /* 通用 */
            /* 防止图片模糊和压缩 */
            -webkit-backface-visibility: hidden !important;
            backface-visibility: hidden !important;
            -webkit-transform: translateZ(0) !important;
            transform: translateZ(0) !important;
            /* 确保无损显示 */
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* 导出模式专用样式 - 保持与预览完全一致 */
        .export-mode .document-header-image-container img {
            /* 导出时保持与预览完全相同的样式 */
            height: 100% !important; /* 完全填充90px高度的页眉容器 */
            width: 100% !important; /* 完全填充容器宽度 */
            max-width: none !important; /* 移除宽度限制 */
            object-fit: cover !important; /* 完全填充容器，保持比例 */
            object-position: center !important; /* 居中对齐 */
            /* 强制最高质量渲染 - 兼容多浏览器 */
            image-rendering: -webkit-optimize-contrast !important; /* Safari/Chrome/Edge */
            image-rendering: crisp-edges !important; /* Firefox/Chrome */
            image-rendering: high-quality !important; /* 通用 */
            /* 确保导出时图片清晰度 */
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }
        
        .document-footer,
        .document-footer-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: var(--footer-height);
            z-index: var(--z-index-footer);
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-top: 1px solid var(--border-color);
            /* 确保页脚在A4边界内 */
            box-sizing: border-box;
            max-width: var(--a4-width-px);
            margin: 0 auto;
        }
        
        .document-footer-content {
            width: 100%;
            text-align: center;
            font-size: 8pt;
            color: #666;
            line-height: 1.2;
            padding: 2px 5px;
        }
        
        /* 统一页脚样式 */
        .unified-document-footer.company-footer-image-container {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: auto;
            min-height: var(--footer-height);
            background-color: white;
            z-index: var(--z-index-footer);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-end;
            padding: 5px 5px 2px 5px;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
            margin-bottom: 0;
        }
        
        .unified-document-footer.company-footer-image-container img {
            /* 完全填充显示 - 固定高度填充策略 */
            height: 100% !important; /* 完全填充70px高度的页脚容器 */
            width: 100% !important; /* 完全填充容器宽度 */
            max-width: none !important; /* 移除最大宽度限制 */
            object-fit: cover !important; /* 完全填充容器，保持比例，可能裁剪 */
            object-position: center !important; /* 居中对齐，确保重要部分可见 */
            margin: 0 !important; /* 移除边距，确保贴合容器边界 */
            display: block !important;
            /* 无损高质量渲染 - 兼容多浏览器 */
            image-rendering: -webkit-optimize-contrast !important; /* Safari/Chrome/Edge */
            image-rendering: crisp-edges !important; /* Firefox/Chrome */
            image-rendering: high-quality !important; /* 通用 */
            /* 防止图片模糊和压缩 */
            -webkit-backface-visibility: hidden !important;
            backface-visibility: hidden !important;
            -webkit-transform: translateZ(0) !important;
            transform: translateZ(0) !important;
            /* 确保无损显示 */
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* 导出模式下的页脚图片优化 - 保持与预览完全一致 */
        .export-mode .unified-document-footer.company-footer-image-container img {
            /* 导出时保持与预览完全相同的样式 */
            height: 100% !important; /* 完全填充70px高度的页脚容器 */
            width: 100% !important; /* 完全填充容器宽度 */
            max-width: none !important; /* 移除宽度限制 */
            object-fit: cover !important; /* 完全填充容器，保持比例 */
            object-position: center !important; /* 居中对齐 */
            /* 强制最高质量渲染 - 兼容多浏览器 */
            image-rendering: -webkit-optimize-contrast !important; /* Safari/Chrome/Edge */
            image-rendering: crisp-edges !important; /* Firefox/Chrome */
            image-rendering: high-quality !important; /* 通用 */
            /* 确保导出时图片清晰度 */
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* 现代化导出UI样式 */
        .modern-export-ui {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 1px solid #cbd5e1;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .modern-export-ui .export-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .modern-export-ui .export-header h3 {
            margin: 0 0 8px 0;
            color: #1e293b;
            font-size: 18px;
            font-weight: 600;
        }

        .modern-export-ui .export-header p {
            margin: 0;
            color: #64748b;
            font-size: 14px;
        }

        .modern-export-ui .export-buttons-row {
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .modern-export-ui .export-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 160px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .modern-export-ui .export-btn.btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
        }

        .modern-export-ui .export-btn.btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
        }

        .modern-export-ui .export-btn.btn-secondary {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
        }

        .modern-export-ui .export-btn.btn-secondary:hover {
            background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(107, 114, 128, 0.4);
        }

        .modern-export-ui .export-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .modern-export-ui .export-progress {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e2e8f0;
        }

        .modern-export-ui .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 12px;
        }

        .modern-export-ui .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .modern-export-ui .progress-text {
            text-align: center;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
        }

        /* #region 简化导出UI样式 */
        .simple-export-ui {
            margin-top: 10px;
        }

        .simple-export-ui .export-buttons-row {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .simple-export-ui .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 120px;
        }

        .simple-export-ui .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .simple-export-ui .btn:active {
            transform: translateY(0);
        }
        /* #endregion */
        
        /* #region 印章样式 */
        .company-stamp {
            position: absolute;
            bottom: var(--stamp-bottom-offset);
            right: var(--stamp-right-offset);
            z-index: var(--z-index-stamp); /* 使用统一的z-index变量 */
            width: 120px;
            height: 120px;
            opacity: 0.6; /* 设置为60%透明度，实现半透明效果 */
            pointer-events: none; /* 避免阻挡其他元素的交互 */
        }

        .company-stamp img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* 印章占位符样式 */
        .stamp-placeholder {
            position: absolute;
            bottom: var(--stamp-bottom-offset);
            right: var(--stamp-right-offset);
            width: 120px;
            height: 120px;
            z-index: var(--z-index-stamp); /* 使用统一的z-index变量 */
            opacity: 0.5; /* 占位符更透明 */
            pointer-events: none;
        }
        /* #endregion */

        /* #region Material Design 增强样式 */
        .total-amount-display {
            font-size: 18px;
            font-weight: bold;
            color: var(--primary-color);
            padding: 12px;
            background: rgba(30, 64, 175, 0.05);
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
        }

        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        /* 预览状态指示器样式 */
        .preview-status-indicator {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .preview-status-indicator.updating {
            background-color: #fef3c7;
            color: #d97706;
            border: 1px solid #fbbf24;
        }

        .preview-status-indicator.success {
            background-color: #dcfce7;
            color: #166534;
            border: 1px solid #86efac;
        }

        .preview-status-indicator.ready {
            background-color: #e0f2fe;
            color: #0369a1;
            border: 1px solid #7dd3fc;
        }

        .preview-status-indicator.error {
            background-color: #fee2e2;
            color: #dc2626;
            border: 1px solid #fca5a5;
        }

        .preview-header > div {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .empty-preview-message {
            text-align: center;
            color: #ffffff;
            margin-top: 200px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }

        /* Material Design Enhanced Styles */
        .form-section, .preview-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15), 0 2px 4px rgba(0,0,0,0.1);
            padding: 24px;
            transition: box-shadow 0.3s ease;
        }

        .form-section:hover, .preview-section:hover {
            box-shadow: 0 8px 24px rgba(0,0,0,0.15), 0 4px 8px rgba(0,0,0,0.1);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1), 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-transform: none;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(45deg, #1e40af, #3b82f6);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #6b7280, #9ca3af);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #10b981, #34d399);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #ef4444, #f87171);
            color: white;
        }

        /* 图片占位区域样式 */
        .image-placeholder {
            border: 2px dashed #bbb;
            background: linear-gradient(135deg, #f5f5f5, #eeeeee);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666666;
            font-size: 12px;
            text-align: center;
            position: relative;
            border-radius: 6px;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            font-weight: 500;
        }

        /* 导出模式下完全隐藏占位符 */
        .export-mode .image-placeholder,
        .export-mode .header-placeholder,
        .export-mode .footer-placeholder,
        .export-mode .stamp-placeholder {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            width: 0 !important;
            height: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* 导出模式下重置缩放，确保使用原始A4尺寸 */
        .export-mode #document-preview {
            transform: scale(1) !important;
            width: var(--a4-width-px) !important;
            min-height: var(--a4-height-px) !important;
            max-height: none !important;
            margin: 0 !important;
            position: relative !important;
        }

        /* 导出模式下确保容器使用完整A4尺寸 */
        .export-mode {
            width: var(--a4-width-px) !important;
            min-height: var(--a4-height-px) !important;
            max-width: var(--a4-width-px) !important;
            max-height: none !important;
            transform: none !important;
            scale: none !important;
            zoom: 1 !important;
            margin: 0 !important;
            padding: 0 !important;
            box-shadow: none !important;
            border: none !important;
        }

        /* 导出模式下的文字排版优化 - 防止文字重叠 */
        .export-mode * {
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            -webkit-hyphens: auto !important;
            hyphens: auto !important;
            text-rendering: optimizeLegibility !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
            /* 防止文字重叠的关键设置 */
            box-sizing: border-box !important;
            position: relative !important;
        }

        /* 导出模式下的表格文字优化 */
        .export-mode table {
            table-layout: fixed !important;
            width: 100% !important;
            border-collapse: collapse !important;
            border-spacing: 0 !important;
        }

        .export-mode table td,
        .export-mode table th {
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            -webkit-hyphens: auto !important;
            hyphens: auto !important;
            white-space: normal !important;
            vertical-align: top !important;
            line-height: 1.4 !important;
            padding: 6px 8px !important;
            box-sizing: border-box !important;
        }

        /* 导出模式下的文本容器优化 */
        .export-mode div,
        .export-mode span,
        .export-mode p {
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            -webkit-hyphens: auto !important;
            hyphens: auto !important;
            line-height: 1.4 !important;
            box-sizing: border-box !important;
        }

        /* 导出模式下的标题优化 */
        .export-mode h1,
        .export-mode h2,
        .export-mode h3 {
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            line-height: 1.3 !important;
            margin: 8px 0 !important;
            box-sizing: border-box !important;
        }

        .header-placeholder {
            height: var(--header-height);
            margin-bottom: 15px;
            position: relative; /* 改为相对定位 */
            width: 100%;
            /* 移除绝对定位相关属性 */
        }

        .footer-placeholder {
            height: var(--footer-height);
            margin-top: 10px;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: var(--z-index-footer);
        }

        .stamp-placeholder {
            width: 120px;
            height: 120px;
            position: absolute;
            bottom: var(--stamp-bottom-offset);
            right: var(--stamp-right-offset);
            z-index: var(--z-index-stamp);
            border-radius: 50%;
        }

        /* 电子生成标识样式 */
        .electronic-signature {
            position: absolute;
            bottom: calc(var(--footer-height) + 20px);
            left: 0;
            right: 0;
            text-align: center;
            font-size: 12px;
            color: #999;
            z-index: 5;
            font-style: italic;
        }

        /* 内容适配和响应式字体 */
        .content-adaptive {
            font-size: clamp(10px, 2vw, 12px);
            line-height: 1.4;
        }

        .content-adaptive h1 {
            font-size: clamp(16px, 3vw, 20px);
            margin-bottom: 10px;
        }

        .content-adaptive h2 {
            font-size: clamp(14px, 2.5vw, 16px);
            margin-bottom: 8px;
        }

        .items-table.adaptive {
            font-size: clamp(9px, 1.8vw, 11px);
        }

        .items-table.adaptive th,
        .items-table.adaptive td {
            padding: clamp(4px, 1vw, 8px);
        }
        /* #endregion */

        /* #region Header Styles */
        .main-title {
            text-align: center;
            margin-bottom: 30px;
            color: var(--primary-color);
            font-size: 28px;
            font-weight: 600;
        }

        .section-title {
            margin-bottom: 20px;
            color: var(--dark-color);
            font-size: 20px;
            font-weight: 600;
        }
        /* #endregion */

        /* #region 响应式设计 */
        /* 平板设备 */
        @media (max-width: 1024px) {
            .container {
                padding: 15px;
            }

            .grid {
                gap: 20px;
            }

            #document-preview {
                --preview-scale-factor: 0.65;
            }

            #preview-container {
                /* 重新计算平板设备的容器尺寸 */
                min-height: calc(var(--a4-height-px) * 0.65 + 80px);
                min-width: calc(var(--a4-width-px) * 0.65 + 60px);
            }
        }

        /* 小屏幕设备 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .form-section,
            .preview-section {
                padding: 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .btn-group {
                flex-direction: column;
                gap: 10px;
            }

            .btn {
                width: 100%;
                padding: 12px 20px;
                font-size: 16px;
            }

            #document-preview {
                --preview-scale-factor: 0.5;
            }

            #preview-container {
                /* 小屏幕设备的容器尺寸调整 */
                min-height: calc(var(--a4-height-px) * 0.5 + 80px);
                min-width: calc(var(--a4-width-px) * 0.5 + 60px);
                padding: 15px;
            }
        }

        /* 移动设备 */
        @media (max-width: 480px) {
            .container {
                padding: 5px;
            }

            .form-section,
            .preview-section {
                padding: 15px;
            }

            .items-table {
                font-size: 12px;
            }

            .items-table th,
            .items-table td {
                padding: 6px 4px;
            }

            .items-table input {
                font-size: 12px;
                padding: 4px;
            }

            .btn {
                font-size: 14px;
                padding: 10px 15px;
            }

            #document-preview {
                --preview-scale-factor: 0.4;
            }

            #preview-container {
                /* 移动设备的容器尺寸调整 */
                min-height: calc(var(--a4-height-px) * 0.4 + 80px);
                min-width: calc(var(--a4-width-px) * 0.4 + 60px);
                padding: 10px;
            }

            h1 {
                font-size: 24px;
            }

            h2 {
                font-size: 20px;
            }
        }

        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            .btn {
                min-height: 44px;
                font-size: 16px;
            }

            input, select, textarea {
                min-height: 44px;
                font-size: 16px;
            }

            .items-table input {
                min-height: 40px;
            }
        }

        /* 多订单管理器样式 */
        .multi-order-container {
            margin-bottom: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }

        .order-manager {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .order-tabs {
            display: flex;
            gap: 5px;
        }

        .order-tab {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .order-tab:hover {
            background: #e9ecef;
        }

        .order-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .order-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .current-order-info {
            padding: 8px 12px;
            background: white;
            border-radius: 4px;
            border: 1px solid #ddd;
            font-size: 14px;
        }

        .current-order-info span {
            margin-right: 15px;
        }

        .order-badge {
            display: inline-block;
            padding: 2px 8px;
            background: var(--primary-color);
            color: white;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .order-column {
            width: 100px;
            text-align: center;
        }

        .order-info {
            font-size: 12px;
            color: #666;
            text-align: center;
        }

        /* 合并视图样式 */
        .combined-view .item-description input,
        .combined-view .item-quantity input,
        .combined-view .item-price input {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            cursor: default;
        }

        .combined-view .item-description input:focus,
        .combined-view .item-quantity input:focus,
        .combined-view .item-price input:focus {
            outline: none;
            box-shadow: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .order-manager {
                flex-direction: column;
                gap: 10px;
            }

            .order-tabs {
                flex-wrap: wrap;
            }

            .current-order-info span {
                display: block;
                margin-bottom: 5px;
            }
        }
        /* #endregion */

        /* #region 打印专用样式 - Print2PDF方案 */
        @media print {
            /* 隐藏不需要打印的元素 */
            .form-section,
            .preview-header,
            .btn,
            .btn-group,
            #export-method-selector,
            #export-method-info,
            .preview-status-indicator,
            .container > h1,
            .grid,
            .empty-preview-message {
                display: none !important;
            }

            /* 重置页面布局为打印优化 */
            body {
                margin: 0;
                padding: 0;
                background: white;
                font-size: 12pt;
                line-height: 1.4;
                color: black;
            }

            /* 预览容器打印样式 */
            .preview-section {
                display: block !important;
                width: 100% !important;
                height: 100% !important;
                margin: 0 !important;
                padding: 0 !important;
                box-shadow: none !important;
                border-radius: 0 !important;
                background: white !important;
            }

            #preview-container {
                display: block !important;
                width: 100% !important;
                height: 100% !important;
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                transform: none !important;
                overflow: visible !important;
            }

            /* A4文档容器打印样式 */
            #document-preview {
                display: block !important;
                width: 210mm !important;
                height: 297mm !important;
                margin: 0 auto !important;
                padding: 0 !important;
                transform: none !important;
                box-shadow: none !important;
                background: white !important;
                page-break-after: always;
            }

            #document-container {
                width: 100% !important;
                height: 100% !important;
                padding: 15mm 10mm !important; /* 10mm左右内边距 */
                margin: 0 !important;
                background: white !important;
                overflow: visible !important;
                box-sizing: border-box !important;
            }

            /* 印章打印样式 - 保持透明度 */
            .company-stamp {
                opacity: 0.6 !important; /* 保持60%透明度 */
                position: absolute !important;
                bottom: 15% !important;
                right: 5% !important;
                width: 120px !important;
                height: 120px !important;
                z-index: 30 !important;
            }

            /* 隐藏空白图片占位符 */
            .image-placeholder,
            .header-placeholder,
            .footer-placeholder,
            .stamp-placeholder {
                display: none !important;
            }

            /* 确保图片正确显示 - 完全填充打印优化 */
            .document-header-image-container img,
            .company-footer-image-container img,
            .unified-document-footer.company-footer-image-container img,
            .company-stamp img {
                object-fit: cover !important; /* 完全填充容器 */
                object-position: center !important;
                /* 打印时强制最高质量渲染 - 兼容多浏览器 */
                image-rendering: -webkit-optimize-contrast !important; /* Safari/Chrome/Edge */
                image-rendering: crisp-edges !important; /* Firefox/Chrome */
                image-rendering: high-quality !important; /* 通用 */
                /* 防止打印时图片模糊和压缩 */
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                -webkit-backface-visibility: hidden !important;
                backface-visibility: hidden !important;
                /* 确保无损打印 */
                -webkit-font-smoothing: antialiased !important;
                -moz-osx-font-smoothing: grayscale !important;
            }

            /* 页眉图片打印专用 - 固定高度填充 */
            .document-header-image-container img {
                height: 90px !important; /* 固定高度90px，完全填满页眉区域 */
                width: auto !important; /* 宽度自动计算，保持原始宽高比 */
                max-width: none !important; /* 移除宽度限制，允许根据比例自由缩放 */
            }

            /* 页脚图片打印专用 - 完全填充 */
            .unified-document-footer.company-footer-image-container img {
                height: 100% !important; /* 完全填充70px高度的页脚容器 */
                width: 100% !important; /* 完全填充容器宽度 */
                max-width: none !important; /* 移除宽度限制 */
                object-fit: cover !important; /* 完全填充容器，保持比例 */
                object-position: center !important; /* 居中对齐 */
            }

            /* 总金额样式优化 */
            .total-amount-container {
                background: white !important;
                color: #1e40af !important;
                border: 1px solid #1e40af !important;
                padding: 8px 12px !important;
                margin: 10px 0 !important;
                font-weight: bold !important;
                z-index: 200 !important;
            }

            /* 项目表格打印样式 */
            .items-table {
                width: 100% !important;
                border-collapse: collapse !important;
                margin: 10px 0 !important;
                font-size: 11pt !important;
            }

            .items-table th,
            .items-table td {
                border: 1px solid #333 !important;
                padding: 6px 8px !important;
                text-align: left !important;
            }

            .items-table th {
                background: #f5f5f5 !important;
                font-weight: bold !important;
            }

            /* 分页控制 */
            .page-break {
                page-break-before: always;
            }

            .no-page-break {
                page-break-inside: avoid;
            }

            /* 页眉页脚打印样式 */
            .document-header,
            .document-header-image-container {
                position: relative !important;
                width: 100% !important;
                margin-bottom: 15px !important;
                text-align: center !important;
                background: white !important; /* 确保白色背景 */
                box-shadow: none !important; /* 移除阴影 */
                border: none !important; /* 移除边框 */
            }

            /* 打印时确保页眉容器没有灰色背景 */
            .document-header {
                background-color: white !important;
                background-image: none !important;
            }

            .document-footer,
            .unified-document-footer {
                position: absolute !important;
                bottom: 10mm !important;
                left: 0 !important;
                right: 0 !important;
                text-align: center !important;
            }
        }
        /* #region 内联样式替换类 */
        /**
         * 替换内联样式的CSS类
         * @description 将内联样式移动到CSS类中，提高代码可维护性
         */

        /* AI填充区域样式 */
        .ai-fill-container {
            border: 2px dashed #3b82f6 !important;
            border-radius: 8px !important;
            padding: 15px !important;
            margin-bottom: 25px !important;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe) !important;
        }

        .ai-fill-header {
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            margin-bottom: 10px !important;
        }

        .ai-fill-title {
            margin: 0 !important;
            color: #1e40af !important;
            font-size: 16px !important;
        }

        .ai-form-row {
            margin-bottom: 15px !important;
        }

        .ai-image-input {
            margin-bottom: 10px !important;
        }

        .ai-image-hint {
            color: #666 !important;
            font-size: 12px !important;
        }

        .ai-status {
            margin-top: 10px !important;
            padding: 8px !important;
            border-radius: 4px !important;
            font-size: 13px !important;
        }

        /* 多订单容器样式 */
        .multi-order-hidden {
            display: none !important;
        }

        /* 表格列样式 */
        .order-column-hidden {
            display: none !important;
        }

        /* 预览区域样式 */
        .preview-controls {
            display: flex !important;
            align-items: center !important;
            gap: 15px !important;
        }

        .export-method-selector {
            margin-right: 10px !important;
            padding: 8px !important;
            border-radius: 4px !important;
            border: 1px solid #ddd !important;
        }

        .export-method-info {
            margin-top: 8px !important;
            padding: 6px 10px !important;
            background: #f0f9ff !important;
            border-radius: 4px !important;
            font-size: 12px !important;
            color: #1e40af !important;
        }

        /* #endregion */

        /* #endregion */
    </style>
</head>
<body>
    <div class="container">
        <h1 class="main-title">
            发票/收据生成器 / Invoice/Receipt Generator
        </h1>
        
        <div class="grid">
            <!-- 左侧：表单输入区域 -->
            <div class="form-section">
                <h2 class="section-title">文档信息输入 / Document Information</h2>

                <!-- AI智能填充区域 -->
                <div class="form-group ai-fill-container">
                    <div class="ai-fill-header">
                        <h3 class="ai-fill-title">🤖 AI智能填充 / AI Smart Fill</h3>
                        <button type="button" class="btn btn-primary" id="ai-fill-btn" onclick="toggleAIFillPanel()">
                            隐藏AI填充 / Hide AI Fill
                        </button>
                    </div>
                    <div id="ai-fill-panel">
                        <div class="form-row ai-form-row">
                            <div class="form-group">
                                <label for="ai-text-input">文本输入 / Text Input</label>
                                <textarea id="ai-text-input" rows="4" placeholder="粘贴订单信息、客户详情或其他相关文本... / Paste order information, customer details or other relevant text..."></textarea>
                            </div>
                            <div class="form-group">
                                <label for="ai-image-input">图片上传 / Image Upload</label>
                                <input type="file" id="ai-image-input" accept="image/jpeg,image/png" class="ai-image-input">
                                <small class="ai-image-hint">支持JPG/PNG格式，最大20MB / Support JPG/PNG, max 20MB</small>
                            </div>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success" onclick="processAIFill()">
                                <span id="ai-process-text">🧠 分析并填充 / Analyze & Fill</span>
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="clearAIInput()">
                                清空输入 / Clear Input
                            </button>
                        </div>
                        <div id="ai-status" class="hidden ai-status"></div>
                    </div>
                </div>

                <!-- 文档类型选择 -->
                <div class="form-group">
                    <label for="document-type">文档类型 / Document Type</label>
                    <select id="document-type">
                        <option value="receipt">收据 / Receipt</option>
                        <option value="invoice">发票 / Invoice</option>
                    </select>
                </div>

                <!-- 公司选择 -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="company-selector">公司 / Company</label>
                        <select id="company-selector">
                            <option value="gomyhire">GoMyHire</option>
                            <option value="sky-mirror">Sky Mirror</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="currency-selector">货币 / Currency</label>
                        <select id="currency-selector">
                            <option value="MYR">马来西亚令吉 (RM) / Malaysian Ringgit</option>
                            <option value="CNY">人民币 (¥) / Chinese Yuan</option>
                        </select>
                    </div>
                </div>

                <!-- 公司信息字段 -->
                <div class="form-group">
                    <label for="company-name">公司名称 / Company Name</label>
                    <input type="text" id="company-name" placeholder="请输入公司名称 / Enter company name">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="tax-id">税号 / Tax ID</label>
                        <input type="text" id="tax-id" placeholder="请输入税号 / Enter tax ID">
                    </div>
                    <div class="form-group">
                        <label for="company-phone">公司电话 / Company Phone</label>
                        <input type="text" id="company-phone" placeholder="请输入公司电话 / Enter company phone">
                    </div>
                </div>

                <div class="form-group">
                    <label for="company-address">公司地址 / Company Address</label>
                    <input type="text" id="company-address" placeholder="请输入公司地址 / Enter company address">
                </div>

                <div class="form-group">
                    <label for="contact-person">负责人姓名 / Contact Person</label>
                    <input type="text" id="contact-person" placeholder="请输入负责人姓名 / Enter contact person name">
                </div>
                
                <!-- 基本信息 -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="document-number">单据号码 / Document Number</label>
                        <input type="text" id="document-number" placeholder="自动生成 / Auto Generate">
                    </div>
                    <div class="form-group">
                        <label for="document-date">日期 / Date</label>
                        <input type="date" id="document-date">
                    </div>
                </div>

                <!-- 客户信息 -->
                <div class="form-group">
                    <label for="customer-name">客户名称 / Customer Name</label>
                    <input type="text" id="customer-name" placeholder="请输入客户名称 / Enter customer name">
                </div>

                <div class="form-group">
                    <label for="channel">渠道 / Channel</label>
                    <input type="text" id="channel" placeholder="请输入渠道名称 / Enter channel name">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="customer-phone">客户电话 / Customer Phone</label>
                        <input type="text" id="customer-phone" placeholder="请输入客户电话 / Enter customer phone">
                    </div>
                    <div class="form-group">
                        <label for="customer-email">客户邮箱 / Customer Email</label>
                        <input type="email" id="customer-email" placeholder="请输入客户邮箱 / Enter customer email">
                    </div>
                </div>

                <!-- 项目明细 -->
                <div class="form-group">
                    <label>项目明细 / Item Details</label>

                    <!-- 多订单管理器 -->
                    <div class="multi-order-container multi-order-hidden" id="multi-order-container">
                        <div class="order-manager">
                            <div class="order-tabs" id="order-tabs">
                                <!-- 动态生成订单标签页 -->
                            </div>
                            <div class="order-controls">
                                <button type="button" class="btn btn-secondary btn-sm" onclick="addNewOrder()">+ 新增订单</button>
                                <select id="display-mode" onchange="switchDisplayMode(this.value)">
                                    <option value="combined">合并显示</option>
                                    <option value="separate">分别显示</option>
                                </select>
                                <button type="button" class="btn btn-info btn-sm" onclick="toggleMultiOrderMode()">切换模式</button>
                            </div>
                        </div>

                        <!-- 当前订单信息 -->
                        <div class="current-order-info" id="current-order-info">
                            <span>当前订单：<strong id="current-order-display">-</strong></span>
                            <span>客户：<strong id="current-customer-display">-</strong></span>
                        </div>
                    </div>

                    <table class="items-table" id="items-table">
                        <thead>
                            <tr>
                                <th class="order-column order-column-hidden" id="order-column-header">订单 / Order</th>
                                <th>项目描述 / Description</th>
                                <th>数量 / Qty</th>
                                <th>单价 / Price</th>
                                <th>金额 / Amount</th>
                                <th>操作 / Action</th>
                            </tr>
                        </thead>
                        <tbody id="items-tbody">
                            <tr>
                                <td class="order-column order-column-hidden"></td>
                                <td><input type="text" placeholder="项目描述 / Item description" class="item-description" title="项目描述 / Item description"></td>
                                <td><input type="number" value="1" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                                <td><input type="number" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                                <td class="item-amount">0.00</td>
                                <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                            </tr>
                        </tbody>
                    </table>
                    <button type="button" class="btn btn-secondary" onclick="addItem()">添加项目 / Add Item</button>
                </div>

                <!-- 总金额 -->
                <div class="form-group">
                    <label>总金额 / Total Amount</label>
                    <div class="total-amount-display">
                        RM <span id="total-amount">0.00</span>
                    </div>
                </div>

                <!-- 备注 -->
                <div class="form-group">
                    <label for="notes">备注 / Notes</label>
                    <textarea id="notes" rows="3" placeholder="请输入备注信息 / Enter notes"></textarea>
                </div>

                <!-- 操作按钮 -->
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="updatePreview()" title="手动刷新预览 / Manual refresh preview">
                        🔄 刷新预览 / Refresh Preview
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearForm()">清空表单 / Clear Form</button>
                </div>
            </div>
            
            <!-- 右侧：预览区域 -->
            <div class="preview-section">
                <div class="preview-header">
                    <h2>文档预览 / Document Preview</h2>
                    <div class="preview-controls">
                        <!-- 预览状态指示器 -->
                        <div id="preview-status" class="preview-status-indicator hidden">
                            <span id="preview-status-text">实时预览已启用 / Live preview enabled</span>
                        </div>
                        <div class="btn-group">
                            <!-- 简化的导出按钮 - 使用export-components.js模块 -->
                            <button type="button" class="btn btn-success export-btn" data-format="pdf">导出PDF / Export PDF</button>
                            <button type="button" class="btn btn-success export-btn" data-format="png">导出PNG / Export PNG</button>
                            <button type="button" class="btn btn-success export-btn" data-format="jpeg">导出JPEG / Export JPEG</button>
                            <button type="button" class="btn btn-secondary" onclick="debugImageExport()" title="诊断图片导出问题">调试 / Debug</button>
                        </div>
                    </div>
                </div>

                <!-- 预览容器 -->
                <div id="preview-container">
                    <div id="document-preview" class="a4-page">
                        <div id="document-container">
                            <div class="empty-preview-message">
                                请填写表单信息并点击"更新预览"按钮<br>
                                Please fill in the form and click "Update Preview" button
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        /**
         * @file 发票/收据生成器JavaScript功能模块 v3.0
         * @description 独立的发票收据生成器，包含所有必要的功能
         * @version 3.0.0
         * <AUTHOR> Team
         * @features
         * - 现代化导出系统：支持300DPI高质量PDF/PNG/JPEG导出
         * - AI智能填充：集成Gemini AI进行智能表单填充
         * - 多订单管理：支持多个订单的合并和分别显示
         * - 事件管理优化：使用事件委托减少重复绑定
         * - 安全DOM操作：防止XSS攻击的安全DOM操作工具
         * - 性能优化：DOM缓存、防抖机制、错误处理
         * @updated 2024年 - 完成代码重构和优化
         */

        // #region 图片资源管理对象
        /**
         * 图片资源管理器 - 存储所有图片的 base64 编码
         * @description 内置图片资源管理，确保独立HTML文件的完整性
         */
        const ImageBase64 = {
            /**
             * 公司标志图片 - 用于文档头部显示
             */
            logos: {
                'sky-mirror': '', // Sky Mirror World Tour 标志占位符
                'gomyhire': ''    // GoMyHire Travel 标志占位符
            },

            /**
             * 页眉图片 - 用于文档顶部装饰
             */
            headers: {
                'sky-mirror': '', // Sky Mirror 页眉图片占位符
                'gomyhire': ''    // GoMyHire 页眉图片占位符
            },

            /**
             * 页脚图片 - 用于文档底部装饰
             */
            footers: {
                'sky-mirror': '', // Sky Mirror 页脚图片占位符
                'gomyhire': ''    // GoMyHire 页脚图片占位符
            },

            /**
             * 印章图片 - 用于文档签章
             */
            stamps: {
                'sky-mirror': '', // Sky Mirror 印章占位符
                'gomyhire': ''    // GoMyHire 印章占位符
            },

            /**
             * 获取公司标志图片
             * @function getLogo - 根据公司代码获取对应的标志图片
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @returns {string} 图片的 base64 编码字符串
             */
            getLogo(company) {
                // 优先使用外部管理器，如果不可用则使用内置数据
                if (typeof logoImageManager !== 'undefined') {
                    return logoImageManager.getLogo(company);
                }
                return this.logos[company] || '';
            },

            /**
             * 获取页眉图片
             * @function getHeader - 根据公司代码获取对应的页眉图片
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @returns {string} 图片的 base64 编码字符串
             */
            getHeader(company) {
                // 优先使用外部管理器，如果不可用则使用内置数据
                if (typeof headerImageManager !== 'undefined') {
                    return headerImageManager.getHeader(company);
                }
                return this.headers[company] || '';
            },

            /**
             * 获取页脚图片
             * @function getFooter - 根据公司代码获取对应的页脚图片
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @returns {string} 图片的 base64 编码字符串
             */
            getFooter(company) {
                // 优先使用外部管理器，如果不可用则使用内置数据
                if (typeof footerImageManager !== 'undefined') {
                    return footerImageManager.getFooter(company);
                }
                return this.footers[company] || '';
            },

            /**
             * 获取印章图片
             * @function getStamp - 根据公司代码获取对应的印章图片
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @returns {string} 图片的 base64 编码字符串
             */
            getStamp(company) {
                // 优先使用外部管理器，如果不可用则使用内置数据
                if (typeof stampImageManager !== 'undefined') {
                    return stampImageManager.getStamp(company);
                }
                return this.stamps[company] || '';
            },

            /**
             * 更新图片资源
             * @function updateImage - 更新指定类型和公司的图片资源
             * @param {string} type - 图片类型 ('logo', 'header', 'footer', 'stamp')
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @param {string} base64 - 图片的 base64 编码
             */
            updateImage(type, company, base64) {
                // 同时更新外部管理器和内置数据，确保兼容性
                switch (type) {
                    case 'logo':
                        this.logos[company] = base64;
                        if (typeof logoImageManager !== 'undefined') {
                            logoImageManager.setLogo(company, base64);
                        }
                        break;
                    case 'header':
                        this.headers[company] = base64;
                        if (typeof headerImageManager !== 'undefined') {
                            headerImageManager.setHeader(company, base64);
                        }
                        break;
                    case 'footer':
                        this.footers[company] = base64;
                        if (typeof footerImageManager !== 'undefined') {
                            footerImageManager.setFooter(company, base64);
                        }
                        break;
                    case 'stamp':
                        this.stamps[company] = base64;
                        if (typeof stampImageManager !== 'undefined') {
                            stampImageManager.setStamp(company, base64);
                        }
                        break;
                }
                console.log(`✅ 已更新${type}图片资源 - 公司: ${company}`);
            }
        };
        // #endregion

        // #region 导出系统已移至独立模块
        // 导出功能已提取到 export-components.js 模块中
        // 请确保已正确引入该模块




        // #endregion

        // #region 图片质量管理器
        /**
         * 图片质量管理器
         * @description 专门处理页眉页脚图片的高质量渲染和导出优化
         */
        const ImageQualityManager = {
            // 固定高度填充策略的尺寸配置
            recommendedSizes: {
                header: { width: 'auto', height: 90 },  // 页眉：固定高度90px，宽度自适应
                footer: { width: 'auto', height: 70 },  // 页脚：固定高度70px，宽度自适应
                stamp: { width: 120, height: 120 }      // 印章：120x120px
            },

            // 图片加载状态缓存
            loadedImages: new Map(),

            /**
             * 初始化图片质量管理器
             * @function init - 初始化图片质量管理器
             */
            init() {
                DebugManager.log('INFO', '图片质量管理器初始化完成');
                return this;
            },

            /**
             * 预加载并优化图片
             * @function preloadImage - 预加载图片并返回优化后的图片元素
             * @param {string} src - 图片源地址
             * @param {string} type - 图片类型 ('header', 'footer', 'stamp')
             * @returns {Promise<HTMLImageElement>} 优化后的图片元素
             */
            async preloadImage(src, type = 'header') {
                if (!src) return null;

                // 检查缓存
                if (this.loadedImages.has(src)) {
                    return this.loadedImages.get(src);
                }

                return new Promise((resolve, reject) => {
                    const img = new Image();

                    // 设置高质量渲染属性
                    img.style.imageRendering = 'high-quality';
                    img.style.imageRendering = '-webkit-optimize-contrast';
                    img.crossOrigin = 'anonymous'; // 避免CORS问题

                    img.onload = () => {
                        // 优化图片尺寸
                        const optimizedImg = this.optimizeImageSize(img, type);

                        // 缓存优化后的图片
                        this.loadedImages.set(src, optimizedImg);

                        DebugManager.log('DEBUG', `图片预加载完成: ${type}`, {
                            原始尺寸: `${img.naturalWidth}x${img.naturalHeight}`,
                            优化后尺寸: `${optimizedImg.width}x${optimizedImg.height}`,
                            推荐尺寸: `${this.recommendedSizes[type].width}x${this.recommendedSizes[type].height}`
                        });

                        resolve(optimizedImg);
                    };

                    img.onerror = (error) => {
                        DebugManager.log('ERROR', `图片加载失败: ${type}`, { src, error });
                        reject(new Error(`图片加载失败: ${src}`));
                    };

                    img.src = src;
                });
            },

            /**
             * 优化图片尺寸 - 固定高度填充策略
             * @function optimizeImageSize - 根据类型优化图片尺寸
             * @param {HTMLImageElement} img - 原始图片元素
             * @param {string} type - 图片类型
             * @returns {HTMLImageElement} 优化后的图片元素
             */
            optimizeImageSize(img, type) {
                const recommended = this.recommendedSizes[type];

                // 对于页眉和页脚，使用固定高度策略
                if (type === 'header' || type === 'footer') {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    // 固定高度，宽度根据原始宽高比计算
                    const aspectRatio = img.naturalWidth / img.naturalHeight;
                    const targetHeight = recommended.height;
                    const targetWidth = targetHeight * aspectRatio;

                    // 设置Canvas尺寸
                    canvas.width = targetWidth;
                    canvas.height = targetHeight;

                    // 启用最高质量缩放
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';

                    // 绘制优化后的图片
                    ctx.drawImage(img, 0, 0, targetWidth, targetHeight);

                    // 创建新的图片元素
                    const optimizedImg = new Image();
                    optimizedImg.src = canvas.toDataURL('image/png');
                    optimizedImg.width = targetWidth;
                    optimizedImg.height = targetHeight;

                    DebugManager.log('DEBUG', `${type}图片尺寸优化`, {
                        原始尺寸: `${img.naturalWidth}x${img.naturalHeight}`,
                        优化后尺寸: `${targetWidth}x${targetHeight}`,
                        固定高度: targetHeight,
                        计算宽度: targetWidth,
                        宽高比: aspectRatio.toFixed(2)
                    });

                    return optimizedImg;
                } else {
                    // 印章等其他图片保持原有逻辑
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    const targetWidth = recommended.width;
                    const targetHeight = recommended.height;

                    canvas.width = targetWidth;
                    canvas.height = targetHeight;

                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';
                    ctx.drawImage(img, 0, 0, targetWidth, targetHeight);

                    const optimizedImg = new Image();
                    optimizedImg.src = canvas.toDataURL('image/png');
                    optimizedImg.width = targetWidth;
                    optimizedImg.height = targetHeight;

                    return optimizedImg;
                }
            },

            /**
             * 等待所有图片加载完成
             * @function waitForAllImages - 等待容器中所有图片加载完成
             * @param {HTMLElement} container - 容器元素
             * @returns {Promise<void>} 加载完成的Promise
             */
            async waitForAllImages(container) {
                const images = container.querySelectorAll('img');
                const loadPromises = Array.from(images).map(img => {
                    if (img.complete) {
                        return Promise.resolve();
                    }

                    return new Promise((resolve, reject) => {
                        img.onload = resolve;
                        img.onerror = reject;

                        // 超时处理
                        setTimeout(() => {
                            DebugManager.log('WARN', '图片加载超时', { src: img.src });
                            resolve(); // 即使超时也继续
                        }, 5000);
                    });
                });

                await Promise.all(loadPromises);
                DebugManager.log('INFO', `所有图片加载完成，共${images.length}张图片`);
            },

            /**
             * 应用高质量样式
             * @function applyHighQualityStyles - 为图片元素应用高质量渲染样式
             * @param {HTMLElement} container - 容器元素
             */
            applyHighQualityStyles(container) {
                const images = container.querySelectorAll('img');
                images.forEach(img => {
                    // 应用高质量渲染样式
                    img.style.imageRendering = 'high-quality';
                    img.style.imageRendering = '-webkit-optimize-contrast';
                    img.style.webkitBackfaceVisibility = 'hidden';
                    img.style.backfaceVisibility = 'hidden';
                    img.style.webkitTransform = 'translateZ(0)';
                    img.style.transform = 'translateZ(0)';

                    // 确保图片完全加载
                    if (!img.complete) {
                        img.style.opacity = '0';
                        img.onload = () => {
                            img.style.opacity = '1';
                            img.style.transition = 'opacity 0.3s ease';
                        };
                    }
                });

                DebugManager.log('DEBUG', `已为${images.length}张图片应用高质量样式`);
            },

            /**
             * 检查图片质量 - 固定高度填充策略
             * @function checkImageQuality - 检查图片是否符合质量标准
             * @param {HTMLImageElement} img - 图片元素
             * @param {string} type - 图片类型
             * @returns {object} 质量检查结果
             */
            checkImageQuality(img, type) {
                const recommended = this.recommendedSizes[type];
                const actual = {
                    width: img.naturalWidth || img.width,
                    height: img.naturalHeight || img.height
                };

                let qualityScore, recommendation, recommendedSize;

                if (type === 'header' || type === 'footer') {
                    // 对于页眉页脚，主要检查高度质量
                    qualityScore = actual.height / recommended.height;
                    const aspectRatio = actual.width / actual.height;
                    const calculatedWidth = Math.round(recommended.height * aspectRatio);

                    recommendedSize = `高度${recommended.height}px，宽度自适应（当前约${calculatedWidth}px）`;
                    recommendation = qualityScore < 1.0 ?
                        `建议使用高度${recommended.height}px或更高的图片，宽度将自动按比例计算` :
                        '图片质量良好，高度充足';
                } else {
                    // 印章等其他图片保持原有逻辑
                    qualityScore = Math.min(
                        actual.width / recommended.width,
                        actual.height / recommended.height
                    );
                    recommendedSize = `${recommended.width}x${recommended.height}`;
                    recommendation = qualityScore < 1.0 ?
                        `建议使用${recommended.width}x${recommended.height}或更高分辨率的图片` :
                        '图片质量良好';
                }

                return {
                    score: qualityScore,
                    isHighQuality: qualityScore >= 1.0,
                    recommendation: recommendation,
                    actualSize: `${actual.width}x${actual.height}`,
                    recommendedSize: recommendedSize,
                    strategy: type === 'header' || type === 'footer' ? '固定高度填充' : '固定尺寸'
                };
            }
        };
        // #endregion

        // #region 调试和日志管理器
        /**
         * 调试和日志管理器
         * @description 统一管理导出过程中的调试信息和错误处理
         */
        const DebugManager = {
            // 调试模式开关
            debugMode: true,

            // 日志级别
            logLevels: {
                ERROR: 0,
                WARN: 1,
                INFO: 2,
                DEBUG: 3
            },

            currentLogLevel: 3, // DEBUG级别

            // 性能监控
            performanceMetrics: {},

            /**
             * 初始化调试管理器
             * @function init - 初始化调试管理器
             */
            init() {
                this.log('INFO', '调试管理器初始化完成');
                return this;
            },

            /**
             * 记录日志
             * @function log - 记录不同级别的日志
             * @param {string} level - 日志级别
             * @param {string} message - 日志消息
             * @param {object} data - 附加数据
             */
            log(level, message, data = null) {
                if (!this.debugMode) return;

                const levelValue = this.logLevels[level] || this.logLevels.INFO;
                if (levelValue > this.currentLogLevel) return;

                const timestamp = new Date().toISOString();
                const logMessage = `[${timestamp}] [${level}] ${message}`;

                switch (level) {
                    case 'ERROR':
                        console.error(logMessage, data);
                        break;
                    case 'WARN':
                        console.warn(logMessage, data);
                        break;
                    case 'INFO':
                        console.info(logMessage, data);
                        break;
                    case 'DEBUG':
                    default:
                        console.log(logMessage, data);
                        break;
                }
            },

            /**
             * 开始性能监控
             * @function startPerformance - 开始监控某个操作的性能
             * @param {string} operation - 操作名称
             */
            startPerformance(operation) {
                this.performanceMetrics[operation] = {
                    startTime: performance.now(),
                    endTime: null,
                    duration: null
                };
                this.log('DEBUG', `开始性能监控: ${operation}`);
            },

            /**
             * 结束性能监控
             * @function endPerformance - 结束监控并记录结果
             * @param {string} operation - 操作名称
             * @returns {number} 操作耗时（毫秒）
             */
            endPerformance(operation) {
                if (!this.performanceMetrics[operation]) {
                    this.log('WARN', `性能监控未找到操作: ${operation}`);
                    return 0;
                }

                const metric = this.performanceMetrics[operation];
                metric.endTime = performance.now();
                metric.duration = metric.endTime - metric.startTime;

                this.log('INFO', `性能监控完成: ${operation}`, {
                    耗时: `${metric.duration.toFixed(2)}ms`,
                    开始时间: metric.startTime,
                    结束时间: metric.endTime
                });

                return metric.duration;
            },

            /**
             * 记录导出状态
             * @function logExportStatus - 记录导出过程的状态信息
             * @param {string} method - 导出方法
             * @param {string} type - 导出类型（PDF/Image）
             * @param {string} status - 状态（开始/成功/失败）
             * @param {object} details - 详细信息
             */
            logExportStatus(method, type, status, details = {}) {
                const logData = {
                    导出方法: method,
                    导出类型: type,
                    状态: status,
                    时间戳: new Date().toISOString(),
                    ...details
                };

                const level = status === '失败' ? 'ERROR' : status === '成功' ? 'INFO' : 'DEBUG';
                this.log(level, `导出${status}: ${method} -> ${type}`, logData);
            },

            /**
             * 记录错误详情
             * @function logError - 记录详细的错误信息
             * @param {Error} error - 错误对象
             * @param {string} context - 错误上下文
             * @param {object} additionalInfo - 附加信息
             */
            logError(error, context, additionalInfo = {}) {
                const errorInfo = {
                    错误消息: error.message,
                    错误堆栈: error.stack,
                    错误上下文: context,
                    浏览器信息: navigator.userAgent,
                    页面URL: window.location.href,
                    时间戳: new Date().toISOString(),
                    ...additionalInfo
                };

                this.log('ERROR', `错误发生: ${context}`, errorInfo);
                return errorInfo;
            },

            /**
             * 获取系统状态
             * @function getSystemStatus - 获取当前系统状态信息
             * @returns {object} 系统状态对象
             */
            getSystemStatus() {
                return {
                    浏览器: navigator.userAgent,
                    视口尺寸: `${window.innerWidth}x${window.innerHeight}`,
                    设备像素比: window.devicePixelRatio,
                    在线状态: navigator.onLine,
                    语言: navigator.language,
                    平台: navigator.platform,
                    内存信息: navigator.deviceMemory ? `${navigator.deviceMemory}GB` : '未知',
                    连接类型: navigator.connection ? navigator.connection.effectiveType : '未知',
                    DOM就绪状态: document.readyState,
                    页面可见性: document.visibilityState
                };
            }
        };
        // #endregion

        // #region 移除旧导出代码 - 已清理完成





        // #endregion

        // #region 全局变量和配置
        /**
         * 全局配置对象
         * @description 存储应用程序的全局配置信息
         */
        const AppConfig = {
            currentCompany: 'gomyhire',     // 当前选中的公司
            currentDocumentType: 'receipt', // 当前文档类型
            currentCurrency: 'MYR',         // 当前货币类型
            itemCounter: 1,                 // 项目计数器
            autoPreview: true,              // 启用自动预览功能，确保AI填充后能自动更新
            geminiApiKey: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',  // Gemini API密钥（硬植入）

            // 多订单支持配置
            multiOrderMode: false,          // 是否启用多订单模式
            multiOrderData: [],             // 多订单数据存储
            currentOrderIndex: 0,           // 当前选中的订单索引
            displayMode: 'separate',        // 显示模式：'separate' | 'combined'
            orderCounter: 1                 // 订单计数器
        };

        /**
         * DOM元素缓存对象
         * @description 缓存常用DOM元素，减少重复查询，提升性能
         */
        const DOMCache = {
            // 表单元素缓存
            documentType: null,
            companySelector: null,
            currencySelector: null,
            documentNumber: null,
            documentDate: null,
            customerName: null,
            channel: null,
            customerPhone: null,
            customerEmail: null,
            notes: null,
            totalAmount: null,
            itemsTable: null,
            itemsTbody: null,

            // 公司信息字段缓存
            companyName: null,
            taxId: null,
            companyAddress: null,
            companyPhone: null,
            contactPerson: null,

            // AI相关元素缓存
            aiFillPanel: null,
            aiFillBtn: null,
            aiTextInput: null,
            aiImageInput: null,
            aiStatus: null,
            aiProcessText: null,

            // 预览相关元素缓存
            documentContainer: null,
            documentPreview: null,
            previewContainer: null,
            previewStatus: null,
            previewStatusText: null,

            // 导出相关元素缓存
            exportButtons: null,
            modernExportUI: null,

            // 多订单相关元素缓存
            multiOrderToggle: null,
            displayModeSelector: null,
            orderTabs: null,
            currentOrderDisplay: null,

            /**
             * 初始化DOM缓存
             * @function initCache - 初始化所有DOM元素缓存
             */
            initCache() {
                console.log('🔄 初始化DOM缓存...');

                // 表单元素
                this.documentType = document.getElementById('document-type');
                this.companySelector = document.getElementById('company-selector');
                this.currencySelector = document.getElementById('currency-selector');
                this.documentNumber = document.getElementById('document-number');
                this.documentDate = document.getElementById('document-date');
                this.customerName = document.getElementById('customer-name');
                this.channel = document.getElementById('channel');
                this.customerPhone = document.getElementById('customer-phone');
                this.customerEmail = document.getElementById('customer-email');
                this.notes = document.getElementById('notes');
                this.totalAmount = document.getElementById('total-amount');
                this.itemsTable = document.getElementById('items-table');
                this.itemsTbody = document.getElementById('items-tbody');

                // 公司信息字段
                this.companyName = document.getElementById('company-name');
                this.taxId = document.getElementById('tax-id');
                this.companyAddress = document.getElementById('company-address');
                this.companyPhone = document.getElementById('company-phone');
                this.contactPerson = document.getElementById('contact-person');

                // AI相关元素
                this.aiFillPanel = document.getElementById('ai-fill-panel');
                this.aiFillBtn = document.getElementById('ai-fill-btn');
                this.aiTextInput = document.getElementById('ai-text-input');
                this.aiImageInput = document.getElementById('ai-image-input');
                this.aiStatus = document.getElementById('ai-status');
                this.aiProcessText = document.getElementById('ai-process-text');

                // 预览相关元素
                this.documentContainer = document.getElementById('document-container');
                this.documentPreview = document.getElementById('document-preview');
                this.previewContainer = document.getElementById('preview-container');
                this.previewStatus = document.getElementById('preview-status');
                this.previewStatusText = document.getElementById('preview-status-text');

                // 导出相关元素
                this.exportButtons = document.querySelector('.btn-group');
                this.modernExportUI = document.querySelector('.modern-export-ui');

                // 多订单相关元素
                this.multiOrderToggle = document.getElementById('multi-order-toggle');
                this.displayModeSelector = document.getElementById('display-mode-selector');
                this.orderTabs = document.getElementById('order-tabs');
                this.currentOrderDisplay = document.getElementById('current-order-display');

                // 统计缓存结果
                const cacheStats = this.getCacheStats();
                console.log('✅ DOM缓存初始化完成:', cacheStats);
            },

            /**
             * 获取缓存统计信息
             * @function getCacheStats - 获取DOM缓存的统计信息
             * @returns {Object} 缓存统计对象
             */
            getCacheStats() {
                const stats = {
                    总元素数: 0,
                    成功缓存: 0,
                    缓存失败: 0,
                    失败元素: []
                };

                for (const key in this) {
                    if (typeof this[key] !== 'function' && key !== 'getCacheStats') {
                        stats.总元素数++;
                        if (this[key]) {
                            stats.成功缓存++;
                        } else {
                            stats.缓存失败++;
                            stats.失败元素.push(key);
                        }
                    }
                }

                return stats;
            },

            /**
             * 刷新特定元素的缓存
             * @function refreshCache - 刷新指定元素的缓存
             * @param {string} elementName - 元素名称
             * @returns {boolean} 是否刷新成功
             */
            refreshCache(elementName) {
                const elementMap = {
                    documentType: 'document-type',
                    companySelector: 'company-selector',
                    currencySelector: 'currency-selector',
                    documentNumber: 'document-number',
                    documentDate: 'document-date',
                    customerName: 'customer-name',
                    channel: 'channel',
                    customerPhone: 'customer-phone',
                    customerEmail: 'customer-email',
                    notes: 'notes',
                    totalAmount: 'total-amount',
                    itemsTable: 'items-table',
                    itemsTbody: 'items-tbody',
                    companyName: 'company-name',
                    taxId: 'tax-id',
                    companyAddress: 'company-address',
                    companyPhone: 'company-phone',
                    contactPerson: 'contact-person',
                    aiFillPanel: 'ai-fill-panel',
                    aiFillBtn: 'ai-fill-btn',
                    aiTextInput: 'ai-text-input',
                    aiImageInput: 'ai-image-input',
                    aiStatus: 'ai-status',
                    aiProcessText: 'ai-process-text',
                    documentContainer: 'document-container',
                    documentPreview: 'document-preview',
                    previewContainer: 'preview-container',
                    previewStatus: 'preview-status',
                    previewStatusText: 'preview-status-text',
                    multiOrderToggle: 'multi-order-toggle',
                    displayModeSelector: 'display-mode-selector',
                    orderTabs: 'order-tabs',
                    currentOrderDisplay: 'current-order-display'
                };

                const elementId = elementMap[elementName];
                if (elementId) {
                    this[elementName] = document.getElementById(elementId);
                    return !!this[elementName];
                } else if (elementName === 'exportButtons') {
                    this.exportButtons = document.querySelector('.btn-group');
                    return !!this.exportButtons;
                } else if (elementName === 'modernExportUI') {
                    this.modernExportUI = document.querySelector('.modern-export-ui');
                    return !!this.modernExportUI;
                }

                return false;
            },

            /**
             * 获取缓存的DOM元素
             * @function get - 获取指定的DOM元素
             * @param {string} elementName - 元素名称
             * @returns {HTMLElement|null} DOM元素或null
             */
            get(elementName) {
                return this[elementName] || null;
            },

            /**
             * 验证缓存有效性
             * @function validateCache - 检查缓存的DOM元素是否仍然有效
             * @returns {Object} 验证结果
             */
            validateCache() {
                const invalidElements = [];
                const validElements = [];

                for (const key in this) {
                    if (typeof this[key] !== 'function' && this[key] && this[key].nodeType === Node.ELEMENT_NODE) {
                        if (document.contains(this[key])) {
                            validElements.push(key);
                        } else {
                            invalidElements.push(key);
                        }
                    }
                }

                if (invalidElements.length > 0) {
                    console.warn('⚠️ 发现失效的DOM缓存元素:', invalidElements);
                    this.initCache(); // 重新初始化缓存
                }

                return {
                    valid: validElements.length,
                    invalid: invalidElements.length,
                    invalidElements: invalidElements
                };
            },

            /**
             * 强制刷新DOM缓存
             * @function forceRefreshCache - 强制重新缓存所有DOM元素
             */
            forceRefreshCache() {
                console.log('🔄 DOM缓存已强制刷新');
                this.initCache();
            }
        };

        /**
         * 货币配置对象
         * @description 存储不同货币的显示信息和格式
         */
        const CurrencyConfig = {
            'MYR': {
                symbol: 'RM',
                name: '马来西亚令吉',
                englishName: 'Malaysian Ringgit',
                code: 'MYR'
            },
            'CNY': {
                symbol: '¥',
                name: '人民币',
                englishName: 'Chinese Yuan',
                code: 'CNY'
            }
        };



        /**
         * 公司信息配置
         * @description 存储不同公司的基本信息
         */
        const CompanyInfo = {
            'gomyhire': {
                name: 'GoMyHire Travel Sdn Bhd',
                address: 'Kuala Lumpur, Malaysia',
                phone: '+60 3-1234 5678',
                email: '<EMAIL>'
            },
            'sky-mirror': {
                name: 'Sky Mirror World Tour',
                address: 'Selangor, Malaysia',
                phone: '+60 3-8765 4321',
                email: '<EMAIL>'
            }
        };
        // #endregion

        // #region 工具函数
        /**
         * 性能优化的防抖函数管理器
         * @description 提供统一的防抖函数管理，避免重复创建，提升性能
         */
        const DebounceManager = {
            timers: new Map(),

            /**
             * 防抖函数
             * @function debounce - 防抖函数，延迟执行
             * @param {Function} func - 要执行的函数
             * @param {number} wait - 延迟时间（毫秒）
             * @param {string} key - 防抖键名，用于管理多个防抖函数
             * @returns {Function} 防抖后的函数
             */
            debounce(func, wait, key = 'default') {
                return (...args) => {
                    if (this.timers.has(key)) {
                        clearTimeout(this.timers.get(key));
                    }

                    const timer = setTimeout(() => {
                        this.timers.delete(key);
                        try {
                            func(...args);
                        } catch (error) {
                            console.error('防抖函数执行错误:', error);
                        }
                    }, wait);

                    this.timers.set(key, timer);
                };
            },

            /**
             * 清除指定的防抖定时器
             * @function clear - 清除指定键名的防抖定时器
             * @param {string} key - 防抖键名
             */
            clear(key) {
                if (this.timers.has(key)) {
                    clearTimeout(this.timers.get(key));
                    this.timers.delete(key);
                }
            },

            /**
             * 清除所有防抖定时器
             * @function clearAll - 清除所有防抖定时器
             */
            clearAll() {
                this.timers.forEach(timer => clearTimeout(timer));
                this.timers.clear();
            }
        };

        /**
         * 兼容性防抖函数已移除
         * @deprecated 请使用 DebounceManager.debounce() 或 safeDebouncedUpdatePreview()
         * @note 为了提高性能和避免重复创建，统一使用 DebounceManager 管理防抖函数
         */

        /**
         * 生成唯一单据号码
         * @function generateDocumentNumber - 生成基于时间戳的唯一单据号码
         * @param {string} type - 文档类型 ('invoice' 或 'receipt')
         * @returns {string} 格式化的单据号码
         */
        function generateDocumentNumber(type) {
            const date = new Date();
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            const prefix = type === 'invoice' ? 'INV' : 'RCP';
            return `${prefix}${year}${month}${day}${hours}${minutes}${seconds}`;
        }

        /**
         * 格式化金额显示
         * @function formatCurrency - 将数字格式化为货币显示格式
         * @param {number} amount - 金额数值
         * @param {boolean} withSymbol - 是否包含货币符号
         * @returns {string} 格式化后的金额字符串
         */
        function formatCurrency(amount, withSymbol = false) {
            const formattedAmount = parseFloat(amount || 0).toFixed(2);
            if (withSymbol) {
                const currency = CurrencyConfig[AppConfig.currentCurrency];
                return `${currency.symbol} ${formattedAmount}`;
            }
            return formattedAmount;
        }

        /**
         * 获取当前货币符号
         * @function getCurrentCurrencySymbol - 获取当前选中货币的符号
         * @returns {string} 货币符号
         */
        function getCurrentCurrencySymbol() {
            return CurrencyConfig[AppConfig.currentCurrency].symbol;
        }

        /**
         * 切换货币类型
         * @function switchCurrency - 切换货币类型并更新显示
         * @param {string} currencyCode - 货币代码 ('MYR' 或 'CNY')
         */
        function switchCurrency(currencyCode) {
            if (CurrencyConfig[currencyCode]) {
                AppConfig.currentCurrency = currencyCode;

                // 保存到localStorage
                localStorage.setItem('smartoffice_currency', currencyCode);

                // 更新所有金额显示
                updateAllCurrencyDisplays();

                // 如果启用自动预览，更新预览
                if (AppConfig.autoPreview) {
                    safeDebouncedUpdatePreview();
                }

                console.log(`货币已切换为: ${CurrencyConfig[currencyCode].name} (${currencyCode})`);
            }
        }

        /**
         * 更新所有货币显示
         * @function updateAllCurrencyDisplays - 更新页面上所有的货币符号显示
         */
        function updateAllCurrencyDisplays() {
            const symbol = getCurrentCurrencySymbol();

            // 更新总金额显示
            const totalAmountContainer = document.querySelector('.total-amount-display');
            if (totalAmountContainer && DOMCache.totalAmount) {
                const currentAmount = DOMCache.totalAmount.textContent;
                totalAmountContainer.innerHTML = `${symbol} <span id="total-amount">${currentAmount}</span>`;
                // 重新缓存更新后的元素
                DOMCache.refreshCache('totalAmount');
            }
        }

        /**
         * 计算项目总金额
         * @function calculateItemAmount - 计算单个项目的总金额
         * @param {number} quantity - 数量
         * @param {number} price - 单价
         * @returns {number} 总金额
         */
        function calculateItemAmount(quantity, price) {
            return (parseFloat(quantity || 0) * parseFloat(price || 0));
        }

        /**
         * 计算所有项目的总金额
         * @function calculateTotalAmount - 计算所有项目的总金额
         * @returns {number} 总金额
         */
        function calculateTotalAmount() {
            let total = 0;

            console.log(`💰 开始计算总金额 - 模式状态:`, {
                多订单模式: AppConfig.multiOrderMode,
                显示模式: AppConfig.displayMode,
                订单数据长度: AppConfig.multiOrderData ? AppConfig.multiOrderData.length : 0,
                当前订单索引: AppConfig.currentOrderIndex
            });

            // 检查是否为多订单合并模式
            if (AppConfig.multiOrderMode && AppConfig.displayMode === 'combined') {
                // 合并模式：从订单数据中计算总金额
                if (AppConfig.multiOrderData && AppConfig.multiOrderData.length > 0) {
                    AppConfig.multiOrderData.forEach((order, orderIndex) => {
                        if (order.items && order.items.length > 0) {
                            order.items.forEach((item, itemIndex) => {
                                const itemAmount = item.amount || 0;
                                total += itemAmount;
                                console.log(`💰 订单${orderIndex + 1}-项目${itemIndex + 1}: ${item.description} = ${formatCurrency(itemAmount)}`);
                            });
                        }
                    });
                    console.log(`💰 合并模式总金额计算完成: ${formatCurrency(total)} (来自${AppConfig.multiOrderData.length}个订单)`);
                } else {
                    console.warn('⚠️ 合并模式但无订单数据，回退到表格计算');
                    total = calculateTotalFromTable();
                }
            } else {
                // 单订单模式或分别显示模式：从表格输入框计算
                total = calculateTotalFromTable();
            }

            console.log(`💰 最终总金额: ${formatCurrency(total)}`);
            return total;
        }

        /**
         * 从表格计算总金额
         * @function calculateTotalFromTable - 从表格输入框计算总金额
         * @returns {number} 总金额
         */
        function calculateTotalFromTable() {
            let total = 0;
            // 优先使用缓存的tbody元素
            const tbody = DOMCache.itemsTbody || document.getElementById('items-tbody');
            const rows = tbody ? tbody.querySelectorAll('tr') : [];

            console.log(`💰 从表格计算总金额 - 表格行数: ${rows.length}`);

            rows.forEach((row, index) => {
                const quantityInput = row.querySelector('.item-quantity');
                const priceInput = row.querySelector('.item-price');
                const descriptionInput = row.querySelector('.item-description');

                // 早期返回：检查必要元素
                if (!quantityInput || !priceInput || !descriptionInput) {
                    console.warn(`⚠️ 表格行${index + 1}: 缺少必要的输入框元素`);
                    return;
                }

                const quantity = parseFloat(quantityInput.value || 0);
                const price = parseFloat(priceInput.value || 0);
                const description = descriptionInput.value.trim();

                // 早期返回：检查有效数据
                if (!description || quantity <= 0 || price < 0) {
                    console.log(`⚠️ 表格行${index + 1}: 跳过空项目或无效数据 (描述: "${description}", 数量: ${quantity}, 价格: ${price})`);
                    return;
                }

                const itemAmount = calculateItemAmount(quantity, price);
                total += itemAmount;
                console.log(`💰 表格行${index + 1}: ${description} (${quantity} × ${price}) = ${formatCurrency(itemAmount)}`);
            });

            console.log(`💰 表格模式总金额计算完成: ${formatCurrency(total)}`);
            return total;
        }

        /**
         * 更新项目金额显示
         * @function updateItemAmount - 更新单个项目的金额显示
         * @param {HTMLElement} row - 项目行元素
         */
        function updateItemAmount(row) {
            try {
                const quantity = parseFloat(row.querySelector('.item-quantity').value || 0);
                const price = parseFloat(row.querySelector('.item-price').value || 0);
                const amount = calculateItemAmount(quantity, price);

                row.querySelector('.item-amount').textContent = formatCurrency(amount);

                // 安全地更新总金额（使用防抖）
                setTimeout(() => {
                    if (!isUpdatingTotal) {
                        updateTotalAmount();
                    }
                }, 100);
            } catch (error) {
                console.error('❌ updateItemAmount错误:', error);
            }
        }

        // 防止updateTotalAmount无限循环的标记
        let isUpdatingTotal = false;

        /**
         * 更新总金额显示
         * @function updateTotalAmount - 更新总金额显示
         */
        function updateTotalAmount() {
            if (isUpdatingTotal) {
                console.log('⚠️ updateTotalAmount正在执行中，跳过重复调用');
                return;
            }

            isUpdatingTotal = true;
            try {
                const total = calculateTotalAmount();
                if (DOMCache.totalAmount) {
                    DOMCache.totalAmount.textContent = formatCurrency(total);
                }

                // 移除自动预览更新调用，避免循环调用
                // 总金额更新不应该触发预览更新，预览更新应该由表单输入事件触发
            } catch (error) {
                console.error('❌ updateTotalAmount错误:', error);
            } finally {
                isUpdatingTotal = false;
            }
        }

        /**
         * HTML安全转义函数
         * @function escapeHtml - 转义HTML特殊字符，确保内容安全显示
         * @param {string} text - 需要转义的文本
         * @returns {string} 转义后的安全文本
         */
        function escapeHtml(text) {
            if (!text) return '';
            return String(text)
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;')
                .replace(/\//g, '&#x2F;');
        }

        /**
         * 强制刷新DOM缓存
         * @function refreshDOMCache - 强制更新DOM元素缓存，确保数据一致性
         */
        function refreshDOMCache() {
            DOMCache.documentType = document.getElementById('document-type');
            DOMCache.documentNumber = document.getElementById('document-number');
            DOMCache.customerName = document.getElementById('customer-name');
            DOMCache.customerPhone = document.getElementById('customer-phone');
            DOMCache.customerEmail = document.getElementById('customer-email');
            DOMCache.notes = document.getElementById('notes');
            DOMCache.itemsTbody = document.querySelector('#items-tbody');
            DOMCache.totalAmount = document.getElementById('total-amount');

            console.log('🔄 DOM缓存已强制刷新');
        }

        /**
         * 收集表单数据（包含新增字段）- 增强版
         * @function collectFormData - 收集所有表单输入数据，确保数据一致性和安全性
         * @returns {Object} 表单数据对象
         */
        function collectFormData() {
            // 强制刷新DOM缓存，确保数据一致性
            refreshDOMCache();

            const items = [];
            // 直接从DOM获取最新数据，不依赖缓存
            const rows = document.querySelectorAll('#items-tbody tr');

            console.log(`📊 收集表单数据 - 模式: ${AppConfig.multiOrderMode ? '多订单' : '单订单'}, 显示: ${AppConfig.displayMode || '标准'}, 行数: ${rows.length}`);

            rows.forEach((row, index) => {
                const descriptionElement = row.querySelector('.item-description');
                const quantityElement = row.querySelector('.item-quantity');
                const priceElement = row.querySelector('.item-price');

                if (!descriptionElement || !quantityElement || !priceElement) {
                    console.warn(`⚠️ 项目 ${index + 1} 缺少必要的输入元素`);
                    return;
                }

                const description = descriptionElement.value.trim();
                const quantity = parseFloat(quantityElement.value || 0);
                const price = parseFloat(priceElement.value || 0);

                console.log(`📝 处理项目 ${index + 1}:`, {
                    原始描述: description,
                    安全描述: escapeHtml(description),
                    数量: quantity,
                    价格: price
                });

                if (description && quantity > 0 && price >= 0) {
                    items.push({
                        description: description, // 保持原始数据用于计算
                        safeDescription: escapeHtml(description), // 安全版本用于显示
                        quantity,
                        price,
                        amount: calculateItemAmount(quantity, price)
                    });
                }
            });

            // 直接从DOM获取最新值，不依赖缓存
            const docType = document.getElementById('document-type')?.value || 'invoice';
            const total = calculateTotalAmount();

            // 直接从DOM获取所有字段的最新值，确保数据一致性
            const documentNumber = document.getElementById('document-number')?.value.trim() || generateDocumentNumber(docType);
            const customerName = document.getElementById('customer-name')?.value.trim() || '';
            const customerPhone = document.getElementById('customer-phone')?.value.trim() || '';
            const customerEmail = document.getElementById('customer-email')?.value.trim() || '';
            const companyName = document.getElementById('company-name')?.value.trim() || '';
            const taxId = document.getElementById('tax-id')?.value.trim() || '';
            const companyAddress = document.getElementById('company-address')?.value.trim() || '';
            const companyPhone = document.getElementById('company-phone')?.value.trim() || '';
            const contactPerson = document.getElementById('contact-person')?.value.trim() || '';
            const channel = document.getElementById('channel')?.value.trim() || '';
            const notes = document.getElementById('notes')?.value.trim() || '';

            const data = {
                documentType: docType,
                documentNumber: documentNumber,
                safeDocumentNumber: escapeHtml(documentNumber),
                date: document.getElementById('document-date')?.value || new Date().toISOString().split('T')[0],

                // 公司信息 - 原始数据和安全版本
                companyName: companyName,
                safeCompanyName: escapeHtml(companyName),
                taxId: taxId,
                safeTaxId: escapeHtml(taxId),
                companyAddress: companyAddress,
                safeCompanyAddress: escapeHtml(companyAddress),
                companyPhone: companyPhone,
                safeCompanyPhone: escapeHtml(companyPhone),
                contactPerson: contactPerson,
                safeContactPerson: escapeHtml(contactPerson),

                // 客户信息 - 原始数据和安全版本
                customerName: customerName,
                safeCustomerName: escapeHtml(customerName),
                channel: channel,
                safeChannel: escapeHtml(channel),
                customerPhone: customerPhone,
                safeCustomerPhone: escapeHtml(customerPhone),
                customerEmail: customerEmail,
                safeCustomerEmail: escapeHtml(customerEmail),

                items: items,
                total: total,
                notes: notes,
                safeNotes: escapeHtml(notes)
            };

            console.log(`📊 表单数据收集完成 - 项目数: ${items.length}, 总金额: ${formatCurrency(total)}`);
            return data;
        }

        /**
         * 控制公司信息字段显示
         * @function toggleCompanyFields - 根据文档类型显示或隐藏公司信息字段
         * @param {string} documentType - 文档类型 ('invoice' 或 'receipt')
         */
        function toggleCompanyFields(documentType) {
            const companyFields = [
                'company-name',
                'tax-id',
                'company-address',
                'company-phone',
                'contact-person'
            ];

            companyFields.forEach(fieldId => {
                const element = SafeDOM.get(fieldId);
                const fieldGroup = element?.closest('.form-group');
                if (fieldGroup) {
                    if (documentType === 'invoice') {
                        fieldGroup.style.display = 'block';
                        fieldGroup.classList.remove('hidden');
                    } else {
                        fieldGroup.style.display = 'none';
                        fieldGroup.classList.add('hidden');
                    }
                }
            });

            // 对于form-row中的字段，需要特殊处理
            const taxIdElement = SafeDOM.get('tax-id');
            const phoneElement = SafeDOM.get('company-phone');
            const taxIdGroup = taxIdElement?.closest('.form-group');
            const phoneGroup = phoneElement?.closest('.form-group');

            if (taxIdGroup && phoneGroup) {
                const formRow = taxIdGroup.closest('.form-row');
                if (formRow) {
                    if (documentType === 'invoice') {
                        formRow.style.display = 'grid';
                        formRow.classList.remove('hidden');
                    } else {
                        formRow.style.display = 'none';
                        formRow.classList.add('hidden');
                    }
                }
            }

            console.log(`字段显示已更新: ${documentType === 'invoice' ? '显示' : '隐藏'}公司信息字段`);
        }

        /**
         * AI智能填充功能模块
         * @description 集成Gemini AI进行智能表单填充
         */

        /**
         * 切换AI填充面板显示
         * @function toggleAIFillPanel - 显示或隐藏AI填充面板
         */
        function toggleAIFillPanel() {
            const panel = DOMCache.aiFillPanel || SafeDOM.get('ai-fill-panel');
            const btn = DOMCache.aiFillBtn || SafeDOM.get('ai-fill-btn');

            if (panel && btn) {
                if (panel.classList.contains('hidden')) {
                    panel.classList.remove('hidden');
                    btn.textContent = '隐藏AI填充 / Hide AI Fill';
                } else {
                    panel.classList.add('hidden');
                    btn.textContent = '启用AI填充 / Enable AI Fill';
                }
            } else {
                console.warn('⚠️ AI填充面板或按钮元素未找到', {
                    panel: !!panel,
                    btn: !!btn
                });
            }
        }

        /**
         * 清空AI输入
         * @function clearAIInput - 清空AI输入框内容
         */
        function clearAIInput() {
            if (DOMCache.aiTextInput) DOMCache.aiTextInput.value = '';
            if (DOMCache.aiImageInput) DOMCache.aiImageInput.value = '';
            hideAIStatus();
        }

        /**
         * 显示AI处理状态
         * @function showAIStatus - 显示AI处理状态信息
         * @param {string} message - 状态消息
         * @param {string} type - 状态类型 ('info', 'success', 'error')
         */
        function showAIStatus(message, type = 'info') {
            const statusDiv = DOMCache.aiStatus || document.getElementById('ai-status');
            if (statusDiv) {
                statusDiv.classList.remove('hidden');
                statusDiv.textContent = message;

                // 设置状态样式
                statusDiv.className = 'ai-status-' + type;
                const styles = {
                    info: { backgroundColor: '#e0f2fe', color: '#0369a1', border: '1px solid #7dd3fc' },
                    success: { backgroundColor: '#dcfce7', color: '#166534', border: '1px solid #86efac' },
                    error: { backgroundColor: '#fee2e2', color: '#dc2626', border: '1px solid #fca5a5' }
                };

                const style = styles[type] || styles.info;
                Object.assign(statusDiv.style, style);
            }
        }

        /**
         * 隐藏AI状态
         * @function hideAIStatus - 隐藏AI状态显示
         */
        function hideAIStatus() {
            const statusDiv = DOMCache.aiStatus || document.getElementById('ai-status');
            if (statusDiv) {
                statusDiv.classList.add('hidden');
            }
        }

        /**
         * 处理AI智能填充
         * @function processAIFill - 处理AI智能填充请求
         */
        async function processAIFill() {
            const textInput = DOMCache.aiTextInput ? DOMCache.aiTextInput.value.trim() : '';
            const imageInput = DOMCache.aiImageInput ? DOMCache.aiImageInput.files[0] : null;

            if (!textInput && !imageInput) {
                showAIStatus('请输入文本或上传图片 / Please enter text or upload image', 'error');
                return;
            }

            const processBtn = DOMCache.aiProcessText || document.getElementById('ai-process-text');
            const originalText = processBtn ? processBtn.textContent : '🧠 分析并填充 / Analyze & Fill';

            try {
                // 显示处理状态
                processBtn.textContent = '🔄 分析中... / Analyzing...';
                showAIStatus('正在连接Gemini AI进行智能分析... / Connecting to Gemini AI for analysis...', 'info');

                let analysisResult;

                if (imageInput) {
                    analysisResult = await processImageWithGemini(imageInput, textInput);
                } else {
                    analysisResult = await processTextWithGemini(textInput);
                }

                if (analysisResult) {
                    console.log('🎯 AI分析结果获取成功，开始应用到表单:', analysisResult);
                    await applyAIResults(analysisResult);
                    showAIStatus('AI分析完成，信息已填充到表单 / AI analysis completed, information filled to form', 'success');

                    // 添加数据填充验证
                    setTimeout(() => {
                        const verificationResults = {
                            documentNumber: document.getElementById('document-number').value,
                            customerName: document.getElementById('customer-name').value,
                            customerPhone: document.getElementById('customer-phone').value,
                            itemsCount: document.querySelectorAll('#items-tbody tr').length,
                            firstItemDescription: document.querySelector('#items-tbody tr .item-description')?.value || '',
                            totalAmount: document.getElementById('total-amount').textContent
                        };
                        console.log('🔍 数据填充验证结果:', verificationResults);
                    }, 1000);
                } else {
                    console.error('❌ AI分析结果为空或无效');
                    showAIStatus('AI分析未能提取有效信息，请检查输入内容 / AI analysis could not extract valid information', 'error');
                }

            } catch (error) {
                console.error('AI处理错误:', error);
                showAIStatus(`AI处理失败: ${error.message} / AI processing failed: ${error.message}`, 'error');
            } finally {
                processBtn.textContent = originalText;
            }
        }

        /**
         * 使用Gemini AI处理文本
         * @function processTextWithGemini - 使用Gemini AI分析文本内容
         * @param {string} text - 要分析的文本
         * @returns {Object|null} 分析结果对象
         */
        async function processTextWithGemini(text) {
            const prompt = `
请分析以下文本，提取相关的发票/收据信息，并以JSON格式返回。请提取以下字段（如果存在）：

字段说明：
- companyName: 公司名称（字符串）
- taxId: 税号（字符串）
- companyAddress: 公司地址（字符串）
- companyPhone: 公司电话（字符串）
- contactPerson: 负责人姓名（字符串）
- documentNumber: 单据号码（字符串，重点识别以下类型的订单标识符）
  * Order ID / 订单ID
  * 订单编号 / Order Number
  * 单号 / Document Number
  * OTA Reference / OTA参考号
  * Booking Reference / 预订参考号
  * Transaction ID / 交易ID
  * Invoice Number / 发票号
  * Receipt Number / 收据号
  * 支持中英文混合识别
  * 识别常见编号格式：字母+数字组合、纯数字、带分隔符的编号等
  * 处理不同标签格式：冒号、等号、空格分隔等
- customerName: 客户名称（字符串）
- channel: 渠道名称（字符串）
- customerPhone: 客户电话（字符串）
- customerEmail: 客户邮箱（字符串）
- items: 项目数组，每个项目包含 {description, quantity, price}
  * description: 项目描述（字符串）
  * quantity: 数量（数字，默认为1）
  * price: 价格（数字，如果文本中没有明确价格信息则设为0，不要使用空字符串）
- notes: 备注信息（字符串）

重要格式要求：
1. price字段必须是数字类型，例如：140, 0, 99.5
2. 不要使用空字符串""作为price值
3. 如果无法确定价格，请设置price为0
4. quantity字段也必须是数字类型，默认为1

要分析的文本：
${text}

请只返回JSON格式的结果，不要包含其他解释文字。如果某个字段无法提取，字符串字段请设为空字符串，数字字段请设为0或1（quantity默认为1）。
特别注意：请仔细查找文本中的各种单据号码、订单号、参考号等标识符，并提取到documentNumber字段中。
`;

            console.log('🔍 Gemini文本分析 - 输入文本:', text.substring(0, 200) + (text.length > 200 ? '...' : ''));

            try {
                console.log('🌐 开始Gemini API调用');
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${AppConfig.geminiApiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: prompt
                            }]
                        }]
                    })
                });

                console.log('🌐 API响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('🌐 API错误响应:', errorText);
                    throw new Error(`Gemini API请求失败: ${response.status} - ${errorText}`);
                }

                const data = await response.json();
                console.log('🌐 API完整响应:', data);

                const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

                console.log('🤖 Gemini文本分析 - 原始响应:', generatedText);

                if (generatedText) {
                    // 使用统一的JSON解析函数
                    const parseResult = parseGeminiJsonResponse(generatedText);
                    if (parseResult.success) {
                        console.log('📋 Gemini文本分析 - 解析成功:', parseResult.data);
                        return parseResult.data;
                    } else {
                        console.warn('⚠️ Gemini文本分析 - JSON解析失败');
                        return null;
                    }
                }

                console.warn('⚠️ Gemini文本分析 - 未能解析有效JSON');
                return null;
            } catch (error) {
                console.error('❌ Gemini文本分析错误:', error);
                throw error;
            }
        }

        /**
         * 解析Gemini AI返回的JSON响应
         * @function parseGeminiJsonResponse - 统一的JSON解析逻辑，支持对象和数组格式
         * @param {string} responseText - Gemini AI的原始响应文本
         * @returns {Object|null} 解析结果对象，包含 {success: boolean, data: any, isArrayFormat: boolean}
         */
        function parseGeminiJsonResponse(responseText) {
            try {
                // 增强JSON提取逻辑，支持markdown代码块格式
                let jsonString = responseText;

                // 首先尝试提取markdown代码块中的JSON
                const markdownMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/);
                if (markdownMatch) {
                    jsonString = markdownMatch[1];
                    console.log('🔍 从markdown代码块中提取JSON');
                }

                // 尝试解析JSON - 优先匹配对象格式，然后数组格式
                let jsonMatch = jsonString.match(/\{[\s\S]*\}/); // 先尝试对象格式
                let isArrayFormat = false;
                if (!jsonMatch) {
                    jsonMatch = jsonString.match(/\[[\s\S]*\]/); // 再尝试数组格式
                    isArrayFormat = true;
                }

                console.log('🔍 JSON匹配结果:', {
                    找到匹配: !!jsonMatch,
                    是数组格式: isArrayFormat,
                    匹配内容预览: jsonMatch ? jsonMatch[0].substring(0, 100) + '...' : '无'
                });

                if (jsonMatch) {
                    try {
                        const parsedResult = JSON.parse(jsonMatch[0]);
                        console.log('📋 Gemini JSON解析成功:', parsedResult);

                        // 验证和修复价格字段
                        if (parsedResult.items && Array.isArray(parsedResult.items)) {
                            parsedResult.items.forEach((item, index) => {
                                if (item.price === '' || item.price === null || item.price === undefined) {
                                    console.log(`🔧 修复项目${index + 1}的空价格字段`);
                                    item.price = 0;
                                } else if (typeof item.price === 'string') {
                                    const numPrice = parseFloat(item.price);
                                    if (!isNaN(numPrice)) {
                                        item.price = numPrice;
                                        console.log(`🔧 转换项目${index + 1}的价格字符串为数字: ${item.price}`);
                                    } else {
                                        item.price = 0;
                                        console.log(`🔧 无效价格字符串，设为0: ${item.price}`);
                                    }
                                }

                                // 同样处理数量字段
                                if (item.quantity === '' || item.quantity === null || item.quantity === undefined) {
                                    item.quantity = 1;
                                } else if (typeof item.quantity === 'string') {
                                    const numQuantity = parseFloat(item.quantity);
                                    if (!isNaN(numQuantity) && numQuantity > 0) {
                                        item.quantity = numQuantity;
                                    } else {
                                        item.quantity = 1;
                                    }
                                }
                            });
                            console.log('🔧 价格字段验证和修复完成:', parsedResult.items);
                        }

                        // 智能处理数组和对象格式
                        if (Array.isArray(parsedResult)) {
                            if (parsedResult.length === 1) {
                                // 单个对象的数组，提取对象内容
                                console.log('🔄 检测到单对象数组，提取对象内容');
                                const singleObject = parsedResult[0];
                                console.log('🔍 单对象详细内容:', singleObject);
                                console.log('🔢 提取的单据号码:', singleObject.documentNumber || '未找到');
                                console.log('🔢 提取的客户名称:', singleObject.customerName || '未找到');
                                return { success: true, data: { isMultiOrder: false, ...singleObject }, isArrayFormat };
                            } else if (parsedResult.length > 1) {
                                // 真正的多订单数组
                                console.log(`🔢 检测到多订单: ${parsedResult.length}个订单`);
                                parsedResult.forEach((order, index) => {
                                    console.log(`📋 订单${index + 1} - 单据号码:`, order.documentNumber || '未找到');
                                });
                                return { success: true, data: { isMultiOrder: true, orders: parsedResult }, isArrayFormat };
                            } else {
                                // 空数组
                                console.warn('⚠️ 解析结果为空数组');
                                return { success: false, data: null, isArrayFormat };
                            }
                        } else {
                            // 单个对象
                            console.log('🔢 提取的单据号码:', parsedResult.documentNumber || '未找到');
                            return { success: true, data: { isMultiOrder: false, ...parsedResult }, isArrayFormat };
                        }
                    } catch (parseError) {
                        console.error('❌ JSON解析失败:', parseError);
                        console.log('🔍 尝试修复JSON格式...');

                        // 尝试修复常见的JSON格式问题
                        const cleanedJson = cleanJsonString(jsonMatch[0]);
                        try {
                            const parsedResult = JSON.parse(cleanedJson);
                            console.log('✅ JSON修复成功，解析结果:', parsedResult);

                            // 应用相同的智能处理逻辑
                            if (Array.isArray(parsedResult)) {
                                if (parsedResult.length === 1) {
                                    return { success: true, data: { isMultiOrder: false, ...parsedResult[0] }, isArrayFormat };
                                } else if (parsedResult.length > 1) {
                                    return { success: true, data: { isMultiOrder: true, orders: parsedResult }, isArrayFormat };
                                } else {
                                    return { success: false, data: null, isArrayFormat };
                                }
                            } else {
                                return { success: true, data: { isMultiOrder: false, ...parsedResult }, isArrayFormat };
                            }
                        } catch (secondError) {
                            console.error('❌ JSON修复失败:', secondError);
                            return { success: false, data: null, isArrayFormat };
                        }
                    }
                }

                console.warn('⚠️ 未能找到有效的JSON内容');
                return { success: false, data: null, isArrayFormat: false };
            } catch (error) {
                console.error('❌ parseGeminiJsonResponse错误:', error);
                return { success: false, data: null, isArrayFormat: false };
            }
        }

        /**
         * 安全的DOM查询工具
         * @description 提供统一的DOM查询和错误处理机制
         */
        const SafeDOM = {
            /**
             * 安全获取元素
             * @function get - 安全获取DOM元素，包含错误处理
             * @param {string} selector - CSS选择器或元素ID
             * @param {string} type - 查询类型 ('id' 或 'selector')
             * @param {HTMLElement} context - 查询上下文，默认为document
             * @returns {HTMLElement|null} DOM元素或null
             */
            get(selector, type = 'id', context = document) {
                try {
                    if (!selector) {
                        console.warn('⚠️ SafeDOM.get: 选择器为空');
                        return null;
                    }

                    let element = null;
                    if (type === 'id') {
                        element = context.getElementById ? context.getElementById(selector) : context.querySelector(`#${selector}`);
                    } else {
                        element = context.querySelector(selector);
                    }

                    if (!element) {
                        console.warn(`⚠️ SafeDOM.get: 未找到元素 "${selector}" (类型: ${type})`);
                    }

                    return element;
                } catch (error) {
                    console.error(`❌ SafeDOM.get错误: 查询"${selector}"失败`, error);
                    return null;
                }
            },

            /**
             * 安全获取多个元素
             * @function getAll - 安全获取多个DOM元素
             * @param {string} selector - CSS选择器
             * @param {HTMLElement} context - 查询上下文，默认为document
             * @returns {NodeList|Array} 元素列表
             */
            getAll(selector, context = document) {
                try {
                    if (!selector) {
                        console.warn('⚠️ SafeDOM.getAll: 选择器为空');
                        return [];
                    }

                    const elements = context.querySelectorAll(selector);
                    if (elements.length === 0) {
                        console.warn(`⚠️ SafeDOM.getAll: 未找到元素 "${selector}"`);
                    }

                    return elements;
                } catch (error) {
                    console.error(`❌ SafeDOM.getAll错误: 查询"${selector}"失败`, error);
                    return [];
                }
            },

            /**
             * 安全设置元素值
             * @function setValue - 安全设置元素的值
             * @param {string} selector - 元素选择器
             * @param {any} value - 要设置的值
             * @param {string} type - 查询类型
             * @returns {boolean} 是否设置成功
             */
            setValue(selector, value, type = 'id') {
                try {
                    const element = this.get(selector, type);
                    if (element) {
                        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA' || element.tagName === 'SELECT') {
                            element.value = value;
                        } else {
                            element.textContent = value;
                        }
                        return true;
                    }
                    return false;
                } catch (error) {
                    console.error(`❌ SafeDOM.setValue错误: 设置"${selector}"值失败`, error);
                    return false;
                }
            },

            /**
             * 安全获取元素值
             * @function getValue - 安全获取元素的值
             * @param {string} selector - 元素选择器
             * @param {string} type - 查询类型
             * @param {any} defaultValue - 默认值
             * @returns {any} 元素值或默认值
             */
            getValue(selector, type = 'id', defaultValue = '') {
                try {
                    const element = this.get(selector, type);
                    if (element) {
                        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA' || element.tagName === 'SELECT') {
                            return element.value || defaultValue;
                        } else {
                            return element.textContent || defaultValue;
                        }
                    }
                    return defaultValue;
                } catch (error) {
                    console.error(`❌ SafeDOM.getValue错误: 获取"${selector}"值失败`, error);
                    return defaultValue;
                }
            },

            /**
             * 检查元素是否存在
             * @function exists - 检查DOM元素是否存在
             * @param {string} selector - 元素选择器
             * @param {string} type - 查询类型
             * @returns {boolean} 元素是否存在
             */
            exists(selector, type = 'id') {
                return !!this.get(selector, type);
            }
        };

        // generateExportFilename 函数已移至 export-components.js 模块

        /**
         * 事件管理器
         * @description 统一管理事件绑定，使用事件委托减少重复绑定
         */
        const EventManager = {
            // 存储已绑定的事件
            boundEvents: new Set(),

            /**
             * 初始化事件委托
             * @function init - 初始化全局事件委托
             */
            init() {
                console.log('🔄 初始化事件管理器...');

                // 全局点击事件委托
                if (!this.boundEvents.has('global-click')) {
                    document.addEventListener('click', this.handleGlobalClick.bind(this));
                    this.boundEvents.add('global-click');
                }

                // 全局输入事件委托
                if (!this.boundEvents.has('global-input')) {
                    document.addEventListener('input', this.handleGlobalInput.bind(this));
                    this.boundEvents.add('global-input');
                }

                // 全局变化事件委托
                if (!this.boundEvents.has('global-change')) {
                    document.addEventListener('change', this.handleGlobalChange.bind(this));
                    this.boundEvents.add('global-change');
                }

                console.log('✅ 事件管理器初始化完成');
            },

            /**
             * 处理全局点击事件
             * @function handleGlobalClick - 统一处理点击事件
             * @param {Event} e - 点击事件对象
             */
            handleGlobalClick(e) {
                const target = e.target;
                const classList = target.classList;

                try {
                    // 导出按钮
                    if (classList.contains('export-btn')) {
                        const format = target.dataset.format;
                        if (format && typeof ModernExportSystem !== 'undefined') {
                            ModernExportSystem.startExport(format);
                        }
                        return;
                    }

                    // 删除项目按钮
                    if (target.textContent.includes('删除') || target.textContent.includes('Delete')) {
                        if (typeof removeItem === 'function') {
                            removeItem(target);
                        }
                        return;
                    }

                    // AI相关按钮
                    if (target.id === 'ai-fill-btn') {
                        if (typeof toggleAIFillPanel === 'function') {
                            toggleAIFillPanel();
                        }
                        return;
                    }

                    // 处理其他特定按钮
                    const buttonActions = {
                        'ai-process-text': () => typeof processAIFill === 'function' && processAIFill(),
                        'clear-ai-input': () => typeof clearAIInput === 'function' && clearAIInput(),
                        'refresh-preview': () => typeof safeDebouncedUpdatePreview === 'function' && safeDebouncedUpdatePreview(),
                        'clear-form': () => typeof clearForm === 'function' && clearForm(),
                        'add-item': () => typeof addItem === 'function' && addItem()
                    };

                    if (target.id && buttonActions[target.id]) {
                        buttonActions[target.id]();
                        return;
                    }

                    // 处理onclick属性（向后兼容）
                    if (target.onclick) {
                        // 让原有的onclick处理器正常执行
                        return;
                    }

                } catch (error) {
                    console.error('❌ 全局点击事件处理错误:', error);
                }
            },

            /**
             * 处理全局输入事件
             * @function handleGlobalInput - 统一处理输入事件
             * @param {Event} e - 输入事件对象
             */
            handleGlobalInput(e) {
                const target = e.target;
                const classList = target.classList;

                try {
                    // 项目数量和价格输入
                    if (classList.contains('item-quantity') || classList.contains('item-price')) {
                        const row = target.closest('tr');
                        if (row && typeof updateItemAmount === 'function') {
                            updateItemAmount(row);
                        }
                        return;
                    }

                    // 项目描述输入（触发预览更新）
                    if (classList.contains('item-description')) {
                        if (AppConfig.autoPreview && typeof safeDebouncedUpdatePreview === 'function') {
                            safeDebouncedUpdatePreview();
                        }
                        return;
                    }

                    // 其他表单字段（触发预览更新）
                    const autoPreviewFields = [
                        'document-number', 'customer-name', 'customer-phone',
                        'customer-email', 'notes', 'company-name', 'tax-id',
                        'company-address', 'company-phone', 'contact-person'
                    ];

                    if (autoPreviewFields.includes(target.id) && AppConfig.autoPreview) {
                        if (typeof safeDebouncedUpdatePreview === 'function') {
                            safeDebouncedUpdatePreview();
                        }
                        return;
                    }

                } catch (error) {
                    console.error('❌ 全局输入事件处理错误:', error);
                }
            },

            /**
             * 处理全局变化事件
             * @function handleGlobalChange - 统一处理变化事件
             * @param {Event} e - 变化事件对象
             */
            handleGlobalChange(e) {
                const target = e.target;

                try {
                    // 文档类型变化
                    if (target.id === 'document-type') {
                        const newType = target.value;
                        const numberInput = SafeDOM.get('document-number');
                        if (numberInput && typeof generateDocumentNumber === 'function') {
                            numberInput.value = generateDocumentNumber(newType);
                        }
                        if (typeof toggleCompanyFields === 'function') {
                            toggleCompanyFields(newType);
                        }
                        if (AppConfig.autoPreview && typeof safeDebouncedUpdatePreview === 'function') {
                            safeDebouncedUpdatePreview();
                        }
                        return;
                    }

                    // 公司选择器变化
                    if (target.id === 'company-selector') {
                        if (typeof loadCompanyInfo === 'function') {
                            loadCompanyInfo(target.value);
                        }
                        return;
                    }

                    // 货币选择器变化
                    if (target.id === 'currency-selector') {
                        if (typeof switchCurrency === 'function') {
                            switchCurrency(target.value);
                        }
                        return;
                    }

                    // 显示模式变化
                    if (target.id === 'display-mode') {
                        if (typeof switchDisplayMode === 'function') {
                            switchDisplayMode(target.value);
                        }
                        return;
                    }

                    // 数字字段变化（触发总金额更新）
                    if (target.type === 'number' && AppConfig.autoPreview) {
                        if (typeof safeDebouncedUpdatePreview === 'function') {
                            safeDebouncedUpdatePreview();
                        }
                        return;
                    }

                } catch (error) {
                    console.error('❌ 全局变化事件处理错误:', error);
                }
            },

            /**
             * 绑定特定元素事件
             * @function bindElementEvent - 为特定元素绑定事件
             * @param {string} selector - 元素选择器
             * @param {string} eventType - 事件类型
             * @param {Function} handler - 事件处理函数
             * @param {string} key - 事件键名（用于防重复绑定）
             */
            bindElementEvent(selector, eventType, handler, key) {
                if (this.boundEvents.has(key)) {
                    return; // 已绑定，跳过
                }

                const element = SafeDOM.get(selector, 'selector');
                if (element) {
                    element.addEventListener(eventType, handler);
                    this.boundEvents.add(key);
                    console.log(`✅ 绑定事件: ${key}`);
                } else {
                    console.warn(`⚠️ 元素未找到，无法绑定事件: ${selector}`);
                }
            },

            /**
             * 获取绑定状态
             * @function getBindingStats - 获取事件绑定统计信息
             * @returns {Object} 绑定统计对象
             */
            getBindingStats() {
                return {
                    总绑定数: this.boundEvents.size,
                    绑定列表: Array.from(this.boundEvents)
                };
            },

            /**
             * 清理所有事件监听器
             * @function cleanup - 清理所有已绑定的事件监听器
             */
            cleanup() {
                // 清理全局事件监听器
                if (this.boundEvents.has('global-click')) {
                    document.removeEventListener('click', this.handleGlobalClick);
                    this.boundEvents.delete('global-click');
                }
                if (this.boundEvents.has('global-input')) {
                    document.removeEventListener('input', this.handleGlobalInput);
                    this.boundEvents.delete('global-input');
                }
                if (this.boundEvents.has('global-change')) {
                    document.removeEventListener('change', this.handleGlobalChange);
                    this.boundEvents.delete('global-change');
                }

                console.log('🧹 事件监听器清理完成');
            }
        };

        /**
         * 安全的DOM操作工具
         * @description 提供安全的DOM操作方法，避免XSS攻击
         */
        const SafeDOMBuilder = {
            /**
             * 安全创建元素
             * @function createElement - 安全创建DOM元素
             * @param {string} tagName - 标签名
             * @param {Object} options - 选项对象
             * @returns {HTMLElement} 创建的元素
             */
            createElement(tagName, options = {}) {
                const element = document.createElement(tagName);

                if (options.className) {
                    element.className = options.className;
                }

                if (options.textContent) {
                    element.textContent = options.textContent;
                }

                if (options.attributes) {
                    Object.entries(options.attributes).forEach(([key, value]) => {
                        element.setAttribute(key, value);
                    });
                }

                if (options.style) {
                    Object.assign(element.style, options.style);
                }

                return element;
            },

            /**
             * 安全创建表格行
             * @function createTableRow - 安全创建表格行
             * @param {Array} cellData - 单元格数据数组
             * @param {Object} options - 选项对象
             * @returns {HTMLTableRowElement} 表格行元素
             */
            createTableRow(cellData, options = {}) {
                const row = document.createElement('tr');

                if (options.className) {
                    row.className = options.className;
                }

                cellData.forEach(cell => {
                    const td = document.createElement('td');

                    if (typeof cell === 'string') {
                        td.textContent = cell;
                    } else if (typeof cell === 'object') {
                        if (cell.html) {
                            // 仅在明确需要HTML时使用，并进行清理
                            td.innerHTML = this.sanitizeHTML(cell.html);
                        } else if (cell.textContent) {
                            td.textContent = cell.textContent;
                        }

                        if (cell.className) {
                            td.className = cell.className;
                        }

                        if (cell.style) {
                            Object.assign(td.style, cell.style);
                        }

                        if (cell.attributes) {
                            Object.entries(cell.attributes).forEach(([key, value]) => {
                                td.setAttribute(key, value);
                            });
                        }
                    }

                    row.appendChild(td);
                });

                return row;
            },

            /**
             * 清理HTML内容
             * @function sanitizeHTML - 基本的HTML清理
             * @param {string} html - 原始HTML
             * @returns {string} 清理后的HTML
             */
            sanitizeHTML(html) {
                // 基本的HTML清理，移除潜在危险的标签和属性
                return html
                    .replace(/<script[^>]*>.*?<\/script>/gi, '')
                    .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '')
                    .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
                    .replace(/javascript:/gi, '');
            },

            /**
             * 安全清空并填充容器
             * @function safeReplaceContent - 安全替换容器内容
             * @param {HTMLElement} container - 目标容器
             * @param {HTMLElement|Array} content - 新内容
             */
            safeReplaceContent(container, content) {
                if (!container) {
                    console.warn('⚠️ SafeDOMBuilder.safeReplaceContent: 容器为空');
                    return;
                }

                // 清空容器
                while (container.firstChild) {
                    container.removeChild(container.firstChild);
                }

                // 添加新内容
                if (Array.isArray(content)) {
                    content.forEach(item => {
                        if (item instanceof HTMLElement) {
                            container.appendChild(item);
                        }
                    });
                } else if (content instanceof HTMLElement) {
                    container.appendChild(content);
                } else if (typeof content === 'string') {
                    container.textContent = content;
                }
            },

            /**
             * 创建空状态消息
             * @function createEmptyMessage - 创建空状态消息元素
             * @param {string} message - 消息文本
             * @param {Object} options - 选项对象
             * @returns {HTMLElement} 消息元素
             */
            createEmptyMessage(message, options = {}) {
                return this.createElement('div', {
                    className: options.className || 'empty-preview-message',
                    textContent: message,
                    style: {
                        padding: '20px',
                        textAlign: 'center',
                        color: '#666',
                        ...options.style
                    }
                });
            }
        };

        /**
         * 清理JSON字符串
         * @function cleanJsonString - 清理和修复常见的JSON格式问题
         * @param {string} jsonStr - 原始JSON字符串
         * @returns {string} 清理后的JSON字符串
         */
        function cleanJsonString(jsonStr) {
            return jsonStr
                .replace(/```json\s*/g, '') // 移除markdown代码块标记
                .replace(/```\s*/g, '')     // 移除结束的代码块标记
                .replace(/,\s*}/g, '}')     // 移除对象末尾多余的逗号
                .replace(/,\s*]/g, ']')     // 移除数组末尾多余的逗号
                .replace(/\n/g, ' ')        // 替换换行符为空格
                .replace(/\r/g, ' ')        // 替换回车符为空格
                .replace(/\t/g, ' ')        // 替换制表符为空格
                .replace(/\s+/g, ' ')       // 合并多个空格
                .trim();                    // 移除首尾空格
        }

        /**
         * 使用Gemini AI处理图片
         * @function processImageWithGemini - 使用Gemini AI分析图片内容
         * @param {File} imageFile - 图片文件
         * @param {string} additionalText - 额外的文本信息
         * @returns {Object|null} 分析结果对象
         */
        async function processImageWithGemini(imageFile, additionalText = '') {
            // 将图片转换为base64
            const base64Image = await fileToBase64(imageFile);
            const base64Data = base64Image.split(',')[1]; // 移除data:image/...;base64,前缀

            const prompt = `
请分析这张图片${additionalText ? '和以下文本' : ''}，提取相关的发票/收据信息，并以JSON格式返回。

${additionalText ? `额外文本信息：${additionalText}` : ''}

请提取以下字段（如果存在）：
- companyName: 公司名称
- taxId: 税号
- companyAddress: 公司地址
- companyPhone: 公司电话
- contactPerson: 负责人姓名
- documentNumber: 单据号码（重点识别以下类型的订单标识符）
  * Order ID / 订单ID
  * 订单编号 / Order Number
  * 单号 / Document Number
  * OTA Reference / OTA参考号
  * Booking Reference / 预订参考号
  * Transaction ID / 交易ID
  * Invoice Number / 发票号
  * Receipt Number / 收据号
  * 支持中英文混合识别
  * 识别常见编号格式：字母+数字组合、纯数字、带分隔符的编号等
  * 处理不同标签格式：冒号、等号、空格分隔等
- customerName: 客户名称
- channel: 渠道名称
- customerPhone: 客户电话
- customerEmail: 客户邮箱
- items: 项目数组，每个项目包含 {description, quantity, price}
- notes: 备注信息

请只返回JSON格式的结果，不要包含其他解释文字。如果某个字段无法提取，请设为空字符串或空数组。
特别注意：请仔细查找图片中的各种单据号码、订单号、参考号等标识符，并提取到documentNumber字段中。
`;

            console.log('🖼️ Gemini图片分析 - 开始处理图片:', imageFile.name, '大小:', (imageFile.size / 1024).toFixed(2) + 'KB');
            if (additionalText) {
                console.log('📝 额外文本信息:', additionalText.substring(0, 100) + (additionalText.length > 100 ? '...' : ''));
            }

            try {
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${AppConfig.geminiApiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [
                                {
                                    text: prompt
                                },
                                {
                                    inline_data: {
                                        mime_type: imageFile.type,
                                        data: base64Data
                                    }
                                }
                            ]
                        }]
                    })
                });

                if (!response.ok) {
                    throw new Error(`Gemini API请求失败: ${response.status}`);
                }

                const data = await response.json();
                const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

                console.log('🤖 Gemini图片分析 - 原始响应:', generatedText);

                if (generatedText) {
                    // 使用统一的JSON解析函数
                    const parseResult = parseGeminiJsonResponse(generatedText);
                    if (parseResult.success) {
                        console.log('📋 Gemini图片分析 - 解析成功:', parseResult.data);
                        return parseResult.data;
                    } else {
                        console.warn('⚠️ Gemini图片分析 - JSON解析失败');
                        return null;
                    }
                }

                console.warn('⚠️ Gemini图片分析 - 未能解析有效JSON');
                return null;
            } catch (error) {
                console.error('❌ Gemini图片分析错误:', error);
                throw error;
            }
        }

        /**
         * 将文件转换为base64
         * @function fileToBase64 - 将文件转换为base64编码
         * @param {File} file - 文件对象
         * @returns {Promise<string>} base64编码字符串
         */
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }

        /**
         * 应用AI分析结果到表单
         * @function applyAIResults - 将AI分析结果填充到表单字段
         * @param {Object} results - AI分析结果对象
         */
        async function applyAIResults(results) {
            if (!results) return;

            console.log('🔄 开始应用AI分析结果到表单:', results);

            // 早期返回：检查多订单结果
            if (!results.isMultiOrder || !results.orders || !Array.isArray(results.orders)) {
                // 继续处理单订单
            } else if (results.orders.length > 1) {
                console.log(`🔄 检测到多订单: ${results.orders.length}个订单，启用多订单模式`);
                await handleMultiOrderResults(results.orders);
                return;
            } else if (results.orders.length === 1) {
                console.log('🔄 检测到单订单（数组格式），使用单订单模式处理');
                await applySingleOrderResults(results.orders[0]);
                return;
            }

            // 单订单处理（移除isMultiOrder标记）
            const singleOrderData = { ...results };
            delete singleOrderData.isMultiOrder;
            await applySingleOrderResults(singleOrderData);
        }

        /**
         * 处理多订单AI结果
         * @function handleMultiOrderResults - 处理多个订单的AI分析结果
         * @param {Array} orders - 订单数组
         */
        async function handleMultiOrderResults(orders) {
            console.log(`🔄 开始处理多订单AI结果: ${orders.length}个订单`);

            // 修复：只有真正的多订单才切换模式
            if (orders.length <= 1) {
                console.log('⚠️ 订单数量不足，不应使用多订单模式，转为单订单处理');
                if (orders.length === 1) {
                    await applySingleOrderResults(orders[0]);
                }
                return;
            }

            // 智能模式切换：自动切换到多订单模式
            if (!AppConfig.multiOrderMode) {
                console.log('🔄 智能模式切换：检测到多订单，自动切换到多订单模式');
                MultiOrderManager.toggleMode();
            }

            // 确保模式状态正确设置
            AppConfig.multiOrderMode = true;
            console.log(`✅ 多订单模式状态确认: ${AppConfig.multiOrderMode}`);

            // 清空现有订单数据
            AppConfig.multiOrderData = [];
            AppConfig.currentOrderIndex = 0;

            // 处理每个订单
            for (let i = 0; i < orders.length; i++) {
                const orderData = orders[i];
                console.log(`📋 处理订单 ${i + 1}/${orders.length}:`, orderData.documentNumber || `订单${i + 1}`);

                // 创建订单对象，确保项目金额正确计算
                const processedItems = (orderData.items || []).map(item => ({
                    ...item,
                    amount: calculateItemAmount(item.quantity || 1, item.price || 0)
                }));

                const newOrder = {
                    orderId: orderData.documentNumber || `ORD${String(AppConfig.orderCounter).padStart(3, '0')}`,
                    orderName: `订单${i + 1}`,
                    documentNumber: orderData.documentNumber || '',
                    customerName: orderData.customerName || '',
                    customerPhone: orderData.customerPhone || '',
                    customerEmail: orderData.customerEmail || '',
                    items: processedItems,
                    notes: orderData.notes || ''
                };

                AppConfig.multiOrderData.push(newOrder);
                AppConfig.orderCounter++;
            }

            // 切换到第一个订单
            AppConfig.currentOrderIndex = 0;
            MultiOrderManager.loadOrderToForm(AppConfig.multiOrderData[0]);
            MultiOrderManager.updateOrderTabs();
            MultiOrderManager.updateCurrentOrderDisplay();

            // 设置默认显示模式为合并显示
            AppConfig.displayMode = 'combined';
            const displayModeSelector = document.getElementById('display-mode');
            if (displayModeSelector) {
                displayModeSelector.value = 'combined';
            }

            // 自动切换到合并显示模式
            MultiOrderManager.showCombinedView();

            // 填充公司信息（所有订单共享）
            const firstOrder = orders[0];
            fillCompanyInfo(firstOrder);

            // 智能切换功能
            await performIntelligentSwitching(firstOrder);

            // 更新总金额和预览
            updateTotalAmount();

            // 多订单AI填充完成后强制更新预览
            console.log('🔄 多订单AI填充完成，强制更新预览');
            setTimeout(() => {
                try {
                    safeDebouncedUpdatePreview();
                    console.log('✅ 多订单预览更新成功');
                } catch (error) {
                    console.error('❌ 多订单预览更新失败:', error);
                    // 备用方案
                    updatePreview();
                }
            }, 300);

            console.log(`🎉 多订单AI结果处理完成: ${orders.length}个订单已创建`);

            // 输出多订单摘要
            const multiOrderSummary = orders.map((order, index) => ({
                序号: index + 1,
                单据号码: order.documentNumber || '未识别',
                客户名称: order.customerName || '未识别',
                项目数量: (order.items && order.items.length) || 0
            }));
            console.table(multiOrderSummary);
        }

        /**
         * 应用单订单AI结果
         * @function applySingleOrderResults - 应用单个订单的AI分析结果
         * @param {Object} results - 单订单AI分析结果
         */
        async function applySingleOrderResults(results) {
            console.log('🔄 开始应用单订单AI结果:', results);
            console.log('🔍 数据详细检查:', {
                documentNumber: results.documentNumber,
                customerName: results.customerName,
                customerPhone: results.customerPhone,
                customerEmail: results.customerEmail,
                companyName: results.companyName,
                itemsCount: results.items ? results.items.length : 0,
                itemsData: results.items,
                notes: results.notes
            });

            // 验证必要的DOM元素是否存在
            const domElements = {
                documentNumber: !!document.getElementById('document-number'),
                customerName: !!document.getElementById('customer-name'),
                customerPhone: !!document.getElementById('customer-phone'),
                itemsTbody: !!document.getElementById('items-tbody')
            };
            console.log('🔍 DOM元素检查:', domElements);

            // 智能模式切换：确保单订单模式
            if (AppConfig.multiOrderMode) {
                console.log('🔄 智能模式切换：检测到单订单，当前为多订单模式');
                console.log('ℹ️ 保持多订单模式，单订单数据将添加到当前订单');
            } else {
                console.log('✅ 当前为单订单模式，直接应用数据');
            }

            // 修复：增强单据号码填充逻辑
            console.log('🔍 检查单据号码:', results.documentNumber, typeof results.documentNumber);
            if (results.documentNumber) {
                // 使用多重DOM选择策略
                let documentNumberInput = null;

                // 尝试从缓存获取
                if (DOMCache.documentNumber) {
                    documentNumberInput = DOMCache.documentNumber;
                    console.log('✅ 从DOMCache获取单据号码输入框');
                } else {
                    // 直接查找DOM元素
                    documentNumberInput = document.getElementById('document-number');
                    console.log('✅ 直接从DOM获取单据号码输入框');
                }

                if (documentNumberInput) {
                    const currentValue = documentNumberInput.value.trim();
                    console.log('🔍 当前单据号码值:', currentValue);

                    // 如果当前单据号码为空，或者是自动生成的格式（INV/RCP开头），则使用AI识别的号码
                    if (!currentValue || currentValue.match(/^(INV|RCP)\d{14}$/)) {
                        documentNumberInput.value = String(results.documentNumber);
                        console.log('✅ 单据号码已填充:', results.documentNumber);

                        // 触发change事件以确保其他监听器响应
                        documentNumberInput.dispatchEvent(new Event('change', { bubbles: true }));
                    } else {
                        console.log('ℹ️ 单据号码已存在，跳过填充:', currentValue);
                    }
                } else {
                    console.error('❌ 无法找到单据号码输入框元素');
                }
            } else {
                console.log('⚠️ AI未识别到单据号码 - 数据中无documentNumber字段');
            }

            // 填充公司信息
            fillCompanyInfo(results);

            // 修复：增强客户信息填充逻辑
            console.log('🔍 开始填充客户信息:', {
                customerName: results.customerName,
                customerPhone: results.customerPhone,
                customerEmail: results.customerEmail,
                channel: results.channel
            });

            // 客户名称
            if (results.customerName) {
                let customerNameInput = DOMCache.customerName || document.getElementById('customer-name');
                if (customerNameInput) {
                    customerNameInput.value = String(results.customerName);
                    console.log('✅ 客户名称已填充:', results.customerName);
                    customerNameInput.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    console.error('❌ 无法找到客户名称输入框元素');
                }
            } else {
                console.log('⚠️ AI未识别到客户名称');
            }

            // 渠道信息
            if (results.channel) {
                let channelInput = DOMCache.channel || document.getElementById('channel');
                if (channelInput) {
                    channelInput.value = String(results.channel);
                    console.log('✅ 渠道已填充:', results.channel);
                    channelInput.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    console.log('⚠️ 渠道输入框不存在，跳过填充');
                }
            }

            // 客户电话
            if (results.customerPhone) {
                let customerPhoneInput = DOMCache.customerPhone || document.getElementById('customer-phone');
                if (customerPhoneInput) {
                    customerPhoneInput.value = String(results.customerPhone);
                    console.log('✅ 客户电话已填充:', results.customerPhone);
                    customerPhoneInput.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    console.error('❌ 无法找到客户电话输入框元素');
                }
            } else {
                console.log('⚠️ AI未识别到客户电话');
            }

            // 客户邮箱
            if (results.customerEmail) {
                let customerEmailInput = DOMCache.customerEmail || document.getElementById('customer-email');
                if (customerEmailInput) {
                    customerEmailInput.value = String(results.customerEmail);
                    console.log('✅ 客户邮箱已填充:', results.customerEmail);
                    customerEmailInput.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    console.log('⚠️ 客户邮箱输入框不存在，跳过填充');
                }
            }

            // 修复：增强备注填充逻辑
            if (results.notes) {
                let notesInput = DOMCache.notes || document.getElementById('notes');
                if (notesInput) {
                    notesInput.value = String(results.notes);
                    console.log('✅ 备注已填充:', results.notes);
                    notesInput.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    console.log('⚠️ 备注输入框不存在，跳过填充');
                }
            }

            // 修复：增强项目数据处理
            console.log('🔍 检查项目数据:', results.items);
            if (results.items && Array.isArray(results.items) && results.items.length > 0) {
                console.log(`🔄 开始处理 ${results.items.length} 个项目`);
                await fillItemsFromAI(results.items);
                console.log('✅ 项目数据已填充:', results.items.length + '个项目');
            } else {
                console.log('⚠️ 无有效项目数据:', {
                    hasItems: !!results.items,
                    isArray: Array.isArray(results.items),
                    length: results.items ? results.items.length : 0
                });
            }

            // 智能切换功能
            await performIntelligentSwitching(results);

            // 修复：强制更新总金额和预览
            console.log('🔄 开始更新总金额和预览');
            updateTotalAmount();

            // 确保AI填充后预览能够自动更新
            console.log('🔍 自动预览状态:', AppConfig.autoPreview);
            console.log('🔄 强制触发预览更新（AI填充完成）');

            // AI填充完成后，无论autoPreview设置如何，都应该更新预览
            // 使用延迟确保DOM更新完成
            setTimeout(() => {
                try {
                    safeDebouncedUpdatePreview();
                    console.log('✅ AI填充后预览更新已触发');
                } catch (error) {
                    console.error('❌ AI填充后预览更新失败:', error);
                    // 备用方案：直接调用updatePreview
                    try {
                        updatePreview();
                        console.log('✅ 使用备用方案更新预览成功');
                    } catch (backupError) {
                        console.error('❌ 备用预览更新也失败:', backupError);
                    }
                }
            }, 200); // 增加延迟时间，确保所有DOM操作完成

            console.log('🎉 AI分析结果已全部应用到表单完成');

            /**
         * 数据验证管道已移除
         * @note 原数据验证管道会导致性能问题和无限循环，已移除
         * @reason 在AI结果应用过程中，验证管道会触发额外的预览更新，
         *         导致递归调用和性能下降，因此采用更轻量级的验证方式
         */

            // 输出填充摘要
            const fillSummary = {
                单据号码: results.documentNumber || '未识别',
                公司名称: results.companyName || '未识别',
                客户名称: results.customerName || '未识别',
                项目数量: (results.items && results.items.length) || 0,
                备注: results.notes ? '已填充' : '未识别'
            };
            console.table(fillSummary);
        }

        // 数据验证状态管理
        let isValidatingDataTransfer = false;
        let dataValidationRetryCount = 0;
        const MAX_DATA_VALIDATION_RETRY = 2;

        /**
         * 验证数据传输管道
         * @function validateDataTransferPipeline - 验证从AI结果到DOM元素的完整数据传输
         * @param {Object} originalResults - 原始AI分析结果
         */
        async function validateDataTransferPipeline(originalResults) {
            // 防止重复验证
            if (isValidatingDataTransfer) {
                console.log('⚠️ 数据传输验证正在进行中，跳过重复调用');
                return;
            }

            // 检查重试次数
            if (dataValidationRetryCount >= MAX_DATA_VALIDATION_RETRY) {
                console.log('⚠️ 数据验证重试次数已达上限，停止验证');
                dataValidationRetryCount = 0;
                return;
            }

            isValidatingDataTransfer = true;
            console.log(`🔍 开始验证数据传输管道 (第${dataValidationRetryCount + 1}次)`);

            // 1. 验证DOM元素状态
            const domValidation = {
                documentNumber: document.getElementById('document-number')?.value || '',
                customerName: document.getElementById('customer-name')?.value || '',
                customerPhone: document.getElementById('customer-phone')?.value || '',
                customerEmail: document.getElementById('customer-email')?.value || '',
                notes: document.getElementById('notes')?.value || '',
                itemsTableRows: document.querySelectorAll('#items-tbody tr').length
            };

            console.log('📋 DOM元素当前状态:', domValidation);

            // 2. 验证表格数据
            const tableValidation = [];
            const rows = document.querySelectorAll('#items-tbody tr');
            rows.forEach((row, index) => {
                const description = row.querySelector('.item-description')?.value || '';
                const quantity = parseFloat(row.querySelector('.item-quantity')?.value || 0);
                const price = parseFloat(row.querySelector('.item-price')?.value || 0);
                const amount = row.querySelector('.item-amount')?.textContent || '';

                tableValidation.push({
                    行号: index + 1,
                    描述: description,
                    数量: quantity,
                    价格: price,
                    金额: amount
                });
            });

            console.log('📊 表格数据验证:', tableValidation);

            // 3. 验证总金额计算
            const calculatedTotal = calculateTotalAmount();
            console.log('💰 总金额验证:', {
                计算结果: formatCurrency(calculatedTotal),
                显示元素: document.getElementById('total-amount')?.textContent || '未找到'
            });

            // 4. 对比原始数据和最终结果
            const comparisonResult = {
                单据号码匹配: originalResults.documentNumber === domValidation.documentNumber,
                客户名称匹配: originalResults.customerName === domValidation.customerName,
                客户电话匹配: originalResults.customerPhone === domValidation.customerPhone,
                项目数量匹配: (originalResults.items?.length || 0) === domValidation.itemsTableRows,
                总金额大于零: calculatedTotal > 0
            };

            console.log('🔍 数据匹配验证:', comparisonResult);

            // 5. 检查失败项目并提供修复建议
            const failures = [];
            if (!comparisonResult.单据号码匹配) {
                failures.push(`单据号码不匹配: 期望"${originalResults.documentNumber}", 实际"${domValidation.documentNumber}"`);
            }
            if (!comparisonResult.客户名称匹配) {
                failures.push(`客户名称不匹配: 期望"${originalResults.customerName}", 实际"${domValidation.customerName}"`);
            }
            if (!comparisonResult.项目数量匹配) {
                failures.push(`项目数量不匹配: 期望${originalResults.items?.length || 0}, 实际${domValidation.itemsTableRows}`);
            }
            if (!comparisonResult.总金额大于零) {
                failures.push(`总金额为零: 计算结果${formatCurrency(calculatedTotal)}`);
            }

            if (failures.length > 0) {
                console.error('❌ 数据传输管道验证失败:', failures);

                // 尝试自动修复（但要防止无限循环）
                dataValidationRetryCount++;
                if (dataValidationRetryCount < MAX_DATA_VALIDATION_RETRY) {
                    console.log(`🔧 尝试自动修复数据传输问题 (第${dataValidationRetryCount}次)`);
                    await attemptDataTransferFix(originalResults);
                } else {
                    console.log('⚠️ 数据传输修复重试次数已达上限，停止修复尝试');
                }
            } else {
                console.log('✅ 数据传输管道验证通过');
                dataValidationRetryCount = 0; // 重置计数器
            }

            isValidatingDataTransfer = false; // 释放验证锁
        }

        /**
         * 尝试修复数据传输问题
         * @function attemptDataTransferFix - 尝试自动修复数据传输失败
         * @param {Object} originalResults - 原始AI分析结果
         */
        async function attemptDataTransferFix(originalResults) {
            console.log('🔧 开始自动修复数据传输问题');

            // 强制重新填充单据号码
            if (originalResults.documentNumber) {
                const docInput = document.getElementById('document-number');
                if (docInput) {
                    docInput.value = String(originalResults.documentNumber);
                    docInput.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('🔧 强制修复单据号码');
                }
            }

            // 强制重新填充客户信息
            if (originalResults.customerName) {
                const nameInput = document.getElementById('customer-name');
                if (nameInput) {
                    nameInput.value = String(originalResults.customerName);
                    nameInput.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('🔧 强制修复客户名称');
                }
            }

            if (originalResults.customerPhone) {
                const phoneInput = document.getElementById('customer-phone');
                if (phoneInput) {
                    phoneInput.value = String(originalResults.customerPhone);
                    phoneInput.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('🔧 强制修复客户电话');
                }
            }

            // 强制重新填充项目数据
            if (originalResults.items && originalResults.items.length > 0) {
                console.log('🔧 强制重新填充项目数据');
                await fillItemsForSingleOrder(originalResults.items);
            }

            // 临时禁用强制更新，防止无限循环
            // updateTotalAmount();
            // updatePreview();

            console.log('🔧 自动修复完成');
            // 移除自动重新验证，防止无限循环
            // 验证将在上层函数中根据重试计数器决定是否继续
        }

        /**
         * 填充公司信息
         * @function fillCompanyInfo - 填充公司相关信息到表单
         * @param {Object} results - 包含公司信息的结果对象
         */
        function fillCompanyInfo(results) {
            if (results.companyName) {
                document.getElementById('company-name').value = results.companyName;
                console.log('✅ 公司名称已填充:', results.companyName);
            }
            if (results.taxId) {
                document.getElementById('tax-id').value = results.taxId;
                console.log('✅ 税号已填充:', results.taxId);
            }
            if (results.companyAddress) {
                document.getElementById('company-address').value = results.companyAddress;
                console.log('✅ 公司地址已填充:', results.companyAddress);
            }
            if (results.companyPhone) {
                document.getElementById('company-phone').value = results.companyPhone;
                console.log('✅ 公司电话已填充:', results.companyPhone);
            }
            if (results.contactPerson) {
                document.getElementById('contact-person').value = results.contactPerson;
                console.log('✅ 负责人已填充:', results.contactPerson);
            }
        }

        /**
         * 执行智能切换功能
         * @function performIntelligentSwitching - 根据AI识别结果执行智能切换
         * @param {Object} results - AI分析结果
         */
        async function performIntelligentSwitching(results) {
            // 1. 根据公司信息自动切换到发票模式
            if (results.companyName || results.taxId) {
                const docTypeSelect = DOMCache.documentType || document.getElementById('document-type');
                if (docTypeSelect && docTypeSelect.value === 'receipt') {
                    docTypeSelect.value = 'invoice';
                    toggleCompanyFields('invoice');
                    console.log('AI检测到公司信息，已自动切换到发票模式');
                }
            }

            // 2. 根据货币信息自动切换货币类型
            if (results.currency) {
                const currencyCode = detectCurrencyFromText(results.currency);
                if (currencyCode && currencyCode !== AppConfig.currentCurrency) {
                    switchCurrency(currencyCode);
                    const currencySelector = DOMCache.currencySelector || document.getElementById('currency-selector');
                    if (currencySelector) {
                        currencySelector.value = currencyCode;
                    }
                    console.log(`AI检测到货币信息，已自动切换到${CurrencyConfig[currencyCode].name}`);
                }
            }

            // 3. 根据公司类型自动切换公司选择器
            if (results.companyName) {
                const companyCode = detectCompanyFromName(results.companyName);
                if (companyCode && companyCode !== AppConfig.currentCompany) {
                    AppConfig.currentCompany = companyCode;
                    const companySelector = DOMCache.companySelector || document.getElementById('company-selector');
                    if (companySelector) {
                        companySelector.value = companyCode;
                    }
                    console.log(`AI检测到公司信息，已自动切换到${companyCode}`);
                }
            }
        }

        /**
         * 从文本中检测货币类型
         * @function detectCurrencyFromText - 从文本中检测货币类型
         * @param {string} text - 包含货币信息的文本
         * @returns {string|null} 货币代码或null
         */
        function detectCurrencyFromText(text) {
            const lowerText = text.toLowerCase();

            // 检测马来西亚令吉
            if (lowerText.includes('rm') || lowerText.includes('ringgit') ||
                lowerText.includes('malaysia') || lowerText.includes('myr')) {
                return 'MYR';
            }

            // 检测人民币
            if (lowerText.includes('¥') || lowerText.includes('yuan') ||
                lowerText.includes('rmb') || lowerText.includes('cny') ||
                lowerText.includes('人民币')) {
                return 'CNY';
            }

            return null;
        }

        /**
         * 从公司名称检测公司代码
         * @function detectCompanyFromName - 从公司名称检测公司代码
         * @param {string} companyName - 公司名称
         * @returns {string|null} 公司代码或null
         */
        function detectCompanyFromName(companyName) {
            const lowerName = companyName.toLowerCase();

            // 检测GoMyHire
            if (lowerName.includes('gomyhire') || lowerName.includes('go my hire')) {
                return 'gomyhire';
            }

            // 检测Sky Mirror
            if (lowerName.includes('sky mirror') || lowerName.includes('skymirror')) {
                return 'sky-mirror';
            }

            return null;
        }

        /**
         * 从AI结果填充项目数据（支持多订单）
         * @function fillItemsFromAI - 从AI分析结果填充项目表格
         * @param {Array} items - 项目数组
         * @param {string} orderId - 订单ID（可选，用于多订单场景）
         */
        async function fillItemsFromAI(items, orderId = null) {
            console.log('🔄 开始填充AI识别的项目数据:', items.length + '个项目');
            console.log('🔍 项目数据详情:', items);

            const tbody = document.getElementById('items-tbody');
            if (!tbody) {
                console.error('❌ 无法找到项目表格tbody元素');
                return;
            }

            console.log('✅ 找到项目表格元素');

            // 根据模式决定处理方式
            if (AppConfig.multiOrderMode && orderId) {
                console.log('🔄 使用多订单模式填充');
                await fillItemsForMultiOrder(items, orderId);
            } else {
                console.log('🔄 使用单订单模式填充');
                await fillItemsForSingleOrder(items);
            }

            // 安全地更新总金额
            console.log('🔄 安全更新总金额');
            setTimeout(() => {
                try {
                    updateTotalAmount();
                    console.log('✅ 总金额更新完成');
                } catch (error) {
                    console.error('❌ 总金额更新失败:', error);
                }
            }, 200);
            console.log('✅ 项目数据填充完成');
        }

        /**
         * 单订单模式填充项目
         * @function fillItemsForSingleOrder - 单订单模式下填充项目
         * @param {Array} items - 项目数组
         */
        async function fillItemsForSingleOrder(items) {
            const tbody = document.getElementById('items-tbody');

            console.log('🔄 单订单模式填充项目 - 输入数据:', items);
            console.log('🔍 表格元素检查:', tbody ? '✅ 找到' : '❌ 未找到');

            if (!tbody) {
                console.error('❌ 无法找到items-tbody元素，项目填充失败');
                return;
            }

            // 清空现有项目
            SafeDOMBuilder.safeReplaceContent(tbody, []);
            console.log('✅ 已清空现有项目');

            // 验证输入数据
            if (!items || !Array.isArray(items) || items.length === 0) {
                console.error('❌ 无效的项目数据:', items);
                return;
            }

            // 添加AI识别的项目
            items.forEach((item, index) => {
                try {
                    // 修复：确保数值类型正确转换和验证
                    const description = String(item.description || '').trim();

                    // 增强数量处理：确保为正整数
                    let quantity = parseFloat(item.quantity);
                    if (isNaN(quantity) || quantity <= 0) {
                        quantity = 1; // 默认数量为1
                    }
                    quantity = Math.max(1, Math.round(quantity)); // 确保为正整数

                    // 增强价格处理：支持多种格式并确保为有效数字
                    let price = 0;
                    if (item.price !== undefined && item.price !== null && item.price !== '') {
                        if (typeof item.price === 'string') {
                            // 清理价格字符串：移除货币符号、空格等
                            const cleanPrice = item.price.replace(/[^\d.-]/g, '');
                            price = parseFloat(cleanPrice);
                        } else {
                            price = parseFloat(item.price);
                        }
                    }

                    // 确保价格为有效的非负数
                    if (isNaN(price) || price < 0) {
                        price = 0;
                    }

                    const amount = quantity * price;

                    console.log(`📝 处理项目 ${index + 1}:`, {
                        描述原始: item.description,
                        描述处理: description,
                        数量原始: item.quantity,
                        数量类型: typeof item.quantity,
                        数量转换: quantity,
                        价格原始: item.price,
                        价格类型: typeof item.price,
                        价格转换: price,
                        金额计算: amount
                    });

                    // 验证必要数据
                    if (!description) {
                        console.warn(`⚠️ 项目 ${index + 1} 缺少描述，跳过`);
                        return;
                    }

                    const row = document.createElement('tr');
                    const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';
                    const currentOrderName = AppConfig.multiOrderMode && AppConfig.multiOrderData[AppConfig.currentOrderIndex]
                        ? AppConfig.multiOrderData[AppConfig.currentOrderIndex].orderName
                        : '';

                    // 使用安全的HTML转义
                    const safeDescription = description.replace(/"/g, '&quot;').replace(/'/g, '&#39;');

                    row.innerHTML = `
                        <td class="order-column" style="display: ${orderColumnDisplay};">
                            <span class="order-badge">${currentOrderName}</span>
                        </td>
                        <td><input type="text" value="${safeDescription}" class="item-description" title="项目描述 / Item description"></td>
                        <td><input type="number" value="${quantity}" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                        <td><input type="number" value="${price}" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                        <td class="item-amount">${formatCurrency(amount)}</td>
                        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                    `;
                    tbody.appendChild(row);

                    // 验证行是否正确添加
                    const addedRow = tbody.lastElementChild;
                    if (!addedRow) {
                        console.error(`❌ 项目 ${index + 1} 行添加失败`);
                        return;
                    }

                    // 为新行绑定事件
                    const quantityInput = addedRow.querySelector('.item-quantity');
                    const priceInput = addedRow.querySelector('.item-price');
                    const descInput = addedRow.querySelector('.item-description');

                    if (quantityInput && priceInput && descInput) {
                        quantityInput.addEventListener('input', () => updateItemAmount(addedRow));
                        priceInput.addEventListener('input', () => updateItemAmount(addedRow));

                        // 为描述输入框添加自动预览更新
                        if (AppConfig.autoPreview) {
                            descInput.addEventListener('input', () => safeDebouncedUpdatePreview());
                        }

                        // 触发change事件以确保计算正确
                        quantityInput.dispatchEvent(new Event('input', { bubbles: true }));
                        priceInput.dispatchEvent(new Event('input', { bubbles: true }));

                        console.log(`✅ 项目 ${index + 1} 已添加到表格，金额: ${formatCurrency(amount)}`);
                    } else {
                        console.error(`❌ 项目 ${index + 1} 输入框元素查找失败`);
                    }
                } catch (error) {
                    console.error(`❌ 处理项目 ${index + 1} 时发生错误:`, error);
                }
            });

            console.log(`✅ 单订单模式填充完成，共 ${items.length} 个项目`);

            // 验证最终结果
            const finalRows = tbody.querySelectorAll('tr');
            console.log(`🔍 最终表格行数: ${finalRows.length}`);
        }

        /**
         * 多订单模式填充项目
         * @function fillItemsForMultiOrder - 多订单模式下填充项目到指定订单
         * @param {Array} items - 项目数组
         * @param {string} orderId - 目标订单ID
         */
        async function fillItemsForMultiOrder(items, orderId) {
            // 查找目标订单
            const orderIndex = AppConfig.multiOrderData.findIndex(order => order.orderId === orderId);
            if (orderIndex === -1) {
                console.warn('⚠️ 未找到指定订单:', orderId);
                return;
            }

            // 保存当前订单数据
            if (AppConfig.currentOrderIndex !== orderIndex) {
                MultiOrderManager.saveCurrentOrderData();
            }

            // 切换到目标订单
            AppConfig.currentOrderIndex = orderIndex;

            // 将项目添加到目标订单
            const targetOrder = AppConfig.multiOrderData[orderIndex];
            targetOrder.items = targetOrder.items || [];

            // 合并项目（避免重复）
            items.forEach(newItem => {
                const existingItem = targetOrder.items.find(item =>
                    item.description === newItem.description
                );

                if (existingItem) {
                    // 更新现有项目
                    existingItem.quantity = newItem.quantity;
                    existingItem.price = newItem.price;
                    existingItem.amount = newItem.quantity * newItem.price;
                } else {
                    // 添加新项目
                    targetOrder.items.push({
                        description: newItem.description,
                        quantity: newItem.quantity,
                        price: newItem.price,
                        amount: newItem.quantity * newItem.price
                    });
                }
            });

            // 重新加载表格显示
            MultiOrderManager.loadOrderToForm(targetOrder);
            MultiOrderManager.updateOrderTabs();
            MultiOrderManager.updateCurrentOrderDisplay();

            console.log(`✅ 已将${items.length}个项目添加到订单${orderId}`);
        }

        // 创建优化的防抖预览更新函数 - 缩短延迟时间以提供更好的实时体验
        const debouncedUpdatePreview = DebounceManager.debounce(updatePreview, 300, 'preview_update');

        /**
         * 性能优化的预览更新管理器
         * @description 管理预览更新的性能优化
         */
        const PreviewManager = {
            isUpdating: false,
            pendingUpdate: false,
            updateRetryCount: 0,
            maxRetries: 3,

            /**
             * 安全的预览更新
             * @function safeUpdatePreview - 安全的预览更新，避免重复调用
             */
            async safeUpdatePreview() {
                if (this.isUpdating) {
                    this.pendingUpdate = true;
                    return;
                }

                // 检查重试次数，防止无限循环
                if (this.updateRetryCount >= this.maxRetries) {
                    console.warn('⚠️ 预览更新重试次数已达上限，停止更新');
                    this.updateRetryCount = 0;
                    this.pendingUpdate = false;
                    return;
                }

                this.isUpdating = true;
                try {
                    // 检查关键DOM元素是否存在
                    const documentContainer = document.getElementById('document-container');
                    const documentPreview = document.getElementById('document-preview');

                    if (!documentContainer && !documentPreview) {
                        console.error('❌ 关键预览元素缺失，跳过预览更新');
                        return;
                    }

                    await updatePreview();

                    // 成功更新，重置重试计数器
                    this.updateRetryCount = 0;

                    // 如果有待处理的更新，再次执行（但要检查重试次数）
                    if (this.pendingUpdate) {
                        this.pendingUpdate = false;
                        this.updateRetryCount++;
                        if (this.updateRetryCount < this.maxRetries) {
                            setTimeout(() => this.safeUpdatePreview(), 100);
                        } else {
                            console.warn('⚠️ 待处理更新重试次数已达上限');
                            this.updateRetryCount = 0;
                        }
                    }
                } catch (error) {
                    console.error('预览更新失败:', error);
                    this.updateRetryCount++;

                    // 只在重试次数未达上限时尝试恢复
                    if (this.updateRetryCount < this.maxRetries) {
                        try {
                            const previewContainer = document.getElementById('document-preview');
                            if (previewContainer && !document.getElementById('document-container')) {
                                console.log(`🔧 尝试恢复预览容器结构 (第${this.updateRetryCount}次)`);
                                previewContainer.innerHTML = `
                                    <div id="document-container">
                                        <div class="empty-preview-message">
                                            预览功能已恢复，请重新更新预览<br>
                                            Preview function restored, please update preview again
                                        </div>
                                    </div>
                                `;
                            }
                        } catch (recoveryError) {
                            console.error('❌ 预览功能恢复失败:', recoveryError);
                        }
                    }
                } finally {
                    this.isUpdating = false;
                }
            }
        };

        // 创建安全的防抖预览更新函数 - 优化延迟时间以提供更好的实时体验
        const safeDebouncedUpdatePreview = DebounceManager.debounce(
            () => {
                try {
                    PreviewManager.safeUpdatePreview();
                } catch (error) {
                    console.error('❌ 防抖预览更新失败:', error);
                    // 显示用户友好的错误提示
                    const container = document.getElementById('document-container');
                    if (container) {
                        container.innerHTML = `
                            <div class="error-message" style="color: red; padding: 20px; text-align: center;">
                                预览更新失败，请检查输入数据<br>
                                Preview update failed, please check input data<br>
                                <small>${error.message}</small>
                            </div>
                        `;
                    }
                }
            },
            200, // 减少延迟时间，提供更快的响应
            'safe_preview_update'
        );

        /**
         * 错误处理和性能监控系统
         * @description 统一的错误处理和性能监控
         */
        const ErrorManager = {
            errors: [],
            maxErrors: 50,

            /**
             * 记录错误
             * @function logError - 记录错误信息
             * @param {Error} error - 错误对象
             * @param {string} context - 错误上下文
             */
            logError(error, context = 'Unknown') {
                const errorInfo = {
                    timestamp: new Date().toISOString(),
                    message: error.message,
                    stack: error.stack,
                    context: context
                };

                this.errors.push(errorInfo);

                // 保持错误日志数量在限制内
                if (this.errors.length > this.maxErrors) {
                    this.errors.shift();
                }

                console.error(`[${context}] 错误:`, error);
            },

            /**
             * 安全执行函数
             * @function safeExecute - 安全执行函数，捕获错误
             * @param {Function} func - 要执行的函数
             * @param {string} context - 执行上下文
             * @param {...any} args - 函数参数
             * @returns {any} 函数执行结果或null
             */
            safeExecute(func, context, ...args) {
                try {
                    return func(...args);
                } catch (error) {
                    this.logError(error, context);
                    return null;
                }
            },

            /**
             * 获取错误统计
             * @function getErrorStats - 获取错误统计信息
             * @returns {Object} 错误统计
             */
            getErrorStats() {
                return {
                    totalErrors: this.errors.length,
                    recentErrors: this.errors.slice(-10),
                    errorsByContext: this.errors.reduce((acc, error) => {
                        acc[error.context] = (acc[error.context] || 0) + 1;
                        return acc;
                    }, {})
                };
            }
        };
        // #endregion

        // #region 多订单管理功能
        /**
         * 多订单管理器
         * @description 管理多个订单的数据和界面
         */
        const MultiOrderManager = {
            /**
             * 切换多订单模式
             * @function toggleMode - 切换单订单/多订单模式
             */
            toggleMode() {
                AppConfig.multiOrderMode = !AppConfig.multiOrderMode;
                const container = document.getElementById('multi-order-container');
                const orderColumn = document.getElementById('order-column-header');
                const orderCells = document.querySelectorAll('.order-column');

                if (AppConfig.multiOrderMode) {
                    container.style.display = 'block';
                    orderColumn.style.display = 'table-cell';
                    orderCells.forEach(cell => cell.style.display = 'table-cell');
                    this.initializeMultiOrderMode();
                    console.log('✅ 已切换到多订单模式');
                } else {
                    container.style.display = 'none';
                    orderColumn.style.display = 'none';
                    orderCells.forEach(cell => cell.style.display = 'none');
                    this.resetToSingleMode();
                    console.log('✅ 已切换到单订单模式');
                }
            },

            /**
             * 初始化多订单模式
             * @function initializeMultiOrderMode - 初始化多订单界面和数据
             */
            initializeMultiOrderMode() {
                if (AppConfig.multiOrderData.length === 0) {
                    // 创建第一个订单
                    this.createOrderFromCurrentForm();
                }
                this.updateOrderTabs();
                this.updateCurrentOrderDisplay();

                // 设置默认显示模式为合并显示
                AppConfig.displayMode = 'combined';
                const displayModeSelector = document.getElementById('display-mode');
                if (displayModeSelector) {
                    displayModeSelector.value = 'combined';
                }

                console.log('✅ 多订单模式初始化完成，默认为合并显示模式');
            },

            /**
             * 从当前表单创建订单
             * @function createOrderFromCurrentForm - 从当前表单数据创建新订单
             */
            createOrderFromCurrentForm() {
                const orderData = {
                    orderId: `ORD${String(AppConfig.orderCounter).padStart(3, '0')}`,
                    orderName: `订单${AppConfig.orderCounter}`,
                    documentNumber: document.getElementById('document-number').value || '',
                    customerName: document.getElementById('customer-name').value || '',
                    customerPhone: document.getElementById('customer-phone').value || '',
                    customerEmail: document.getElementById('customer-email').value || '',
                    items: this.getCurrentItems(),
                    notes: document.getElementById('notes').value || ''
                };

                AppConfig.multiOrderData.push(orderData);
                AppConfig.orderCounter++;
                console.log('📋 已创建新订单:', orderData.orderId);
                return orderData;
            },

            /**
             * 获取当前表格中的项目数据
             * @function getCurrentItems - 获取当前项目表格中的所有项目
             * @returns {Array} 项目数组
             */
            getCurrentItems() {
                const items = [];
                const rows = document.querySelectorAll('#items-tbody tr');

                rows.forEach(row => {
                    const description = row.querySelector('.item-description').value.trim();
                    const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
                    const price = parseFloat(row.querySelector('.item-price').value) || 0;

                    if (description || quantity > 0 || price > 0) {
                        items.push({
                            description,
                            quantity,
                            price,
                            amount: quantity * price
                        });
                    }
                });

                return items;
            },

            /**
             * 更新订单标签页
             * @function updateOrderTabs - 更新订单标签页显示
             */
            updateOrderTabs() {
                const tabsContainer = document.getElementById('order-tabs');
                if (!tabsContainer) return;

                tabsContainer.innerHTML = '';

                AppConfig.multiOrderData.forEach((order, index) => {
                    const tab = document.createElement('button');
                    tab.className = `order-tab ${index === AppConfig.currentOrderIndex ? 'active' : ''}`;
                    tab.textContent = order.orderName;
                    tab.onclick = () => this.switchToOrder(index);
                    tabsContainer.appendChild(tab);
                });
            },

            /**
             * 更新当前订单显示
             * @function updateCurrentOrderDisplay - 更新当前订单信息显示
             */
            updateCurrentOrderDisplay() {
                const currentOrder = AppConfig.multiOrderData[AppConfig.currentOrderIndex];
                if (!currentOrder) return;

                const orderDisplay = document.getElementById('current-order-display');
                const customerDisplay = document.getElementById('current-customer-display');

                if (orderDisplay) orderDisplay.textContent = currentOrder.documentNumber || currentOrder.orderId;
                if (customerDisplay) customerDisplay.textContent = currentOrder.customerName || '未填写';
            },

            /**
             * 切换到指定订单
             * @function switchToOrder - 切换到指定索引的订单
             * @param {number} orderIndex - 订单索引
             */
            switchToOrder(orderIndex) {
                if (orderIndex < 0 || orderIndex >= AppConfig.multiOrderData.length) return;

                // 保存当前订单数据
                if (AppConfig.currentOrderIndex !== orderIndex) {
                    this.saveCurrentOrderData();
                }

                // 切换到新订单
                AppConfig.currentOrderIndex = orderIndex;
                this.loadOrderToForm(AppConfig.multiOrderData[orderIndex]);
                this.updateOrderTabs();
                this.updateCurrentOrderDisplay();

                // 根据当前显示模式更新单据号码
                if (AppConfig.displayMode === 'combined') {
                    this.updateCombinedDocumentNumber();
                } else {
                    // 分别显示模式下恢复当前订单的独立单据号码
                    const currentOrder = AppConfig.multiOrderData[orderIndex];
                    const documentNumberInput = document.getElementById('document-number');
                    if (documentNumberInput && currentOrder.documentNumber) {
                        documentNumberInput.value = currentOrder.documentNumber;
                    }
                }

                console.log(`🔄 已切换到订单: ${AppConfig.multiOrderData[orderIndex].orderId}`);
            },

            /**
             * 保存当前订单数据
             * @function saveCurrentOrderData - 保存当前表单数据到订单
             */
            saveCurrentOrderData() {
                const currentOrder = AppConfig.multiOrderData[AppConfig.currentOrderIndex];
                if (!currentOrder) return;

                currentOrder.documentNumber = document.getElementById('document-number').value;
                currentOrder.customerName = document.getElementById('customer-name').value;
                currentOrder.customerPhone = document.getElementById('customer-phone').value;
                currentOrder.customerEmail = document.getElementById('customer-email').value;
                currentOrder.items = this.getCurrentItems();
                currentOrder.notes = document.getElementById('notes').value;

                console.log('💾 已保存订单数据:', currentOrder.orderId);
            },

            /**
             * 加载订单数据到表单
             * @function loadOrderToForm - 将订单数据加载到表单
             * @param {Object} orderData - 订单数据
             */
            loadOrderToForm(orderData) {
                // 填充基本信息
                document.getElementById('document-number').value = orderData.documentNumber || '';
                document.getElementById('customer-name').value = orderData.customerName || '';
                document.getElementById('customer-phone').value = orderData.customerPhone || '';
                document.getElementById('customer-email').value = orderData.customerEmail || '';
                document.getElementById('notes').value = orderData.notes || '';

                // 填充项目数据
                this.loadItemsToTable(orderData.items || []);

                // 更新总金额
                updateTotalAmount();
            },

            /**
             * 加载项目到表格
             * @function loadItemsToTable - 将项目数据加载到表格
             * @param {Array} items - 项目数组
             */
            loadItemsToTable(items) {
                const tbody = document.getElementById('items-tbody');
                tbody.innerHTML = '';

                if (items.length === 0) {
                    // 添加空行
                    addItem();
                    return;
                }

                items.forEach(item => {
                    const row = document.createElement('tr');
                    const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';
                    const currentOrderName = AppConfig.multiOrderData[AppConfig.currentOrderIndex]?.orderName || '';

                    row.innerHTML = `
                        <td class="order-column" style="display: ${orderColumnDisplay};">
                            <span class="order-badge">${currentOrderName}</span>
                        </td>
                        <td><input type="text" value="${item.description || ''}" class="item-description" title="项目描述 / Item description"></td>
                        <td><input type="number" value="${item.quantity || 1}" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                        <td><input type="number" value="${item.price || 0}" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                        <td class="item-amount">${formatCurrency(item.amount || 0)}</td>
                        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                    `;

                    tbody.appendChild(row);

                    // 绑定事件
                    const quantityInput = row.querySelector('.item-quantity');
                    const priceInput = row.querySelector('.item-price');
                    const descInput = row.querySelector('.item-description');

                    quantityInput.addEventListener('input', () => updateItemAmount(row));
                    priceInput.addEventListener('input', () => updateItemAmount(row));

                    if (AppConfig.autoPreview) {
                        descInput.addEventListener('input', () => safeDebouncedUpdatePreview());
                    }
                });
            },

            /**
             * 重置到单订单模式
             * @function resetToSingleMode - 重置到单订单模式
             */
            resetToSingleMode() {
                AppConfig.multiOrderData = [];
                AppConfig.currentOrderIndex = 0;
                AppConfig.orderCounter = 1;

                // 重新加载表格，移除订单列
                const tbody = document.getElementById('items-tbody');
                const rows = tbody.querySelectorAll('tr');

                rows.forEach(row => {
                    const orderCell = row.querySelector('.order-column');
                    if (orderCell) {
                        orderCell.style.display = 'none';
                    }
                });
            },

            /**
             * 显示合并视图
             * @function showCombinedView - 在单个表格中显示所有订单的项目
             */
            showCombinedView() {
                console.log('🔄 切换到合并显示模式');

                if (AppConfig.multiOrderData.length === 0) {
                    console.warn('⚠️ 没有订单数据可显示');
                    return;
                }

                // 保存当前订单数据
                this.saveCurrentOrderData();

                // 清空表格并添加合并视图样式
                const tbody = document.getElementById('items-tbody');
                const table = document.getElementById('items-table');
                tbody.innerHTML = '';
                table.classList.add('combined-view');

                let totalAmount = 0;
                let totalItems = 0;

                // 遍历所有订单，添加项目到表格
                AppConfig.multiOrderData.forEach((order, orderIndex) => {
                    if (order.items && order.items.length > 0) {
                        order.items.forEach((item, itemIndex) => {
                            const row = document.createElement('tr');
                            const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';

                            row.innerHTML = `
                                <td class="order-column" style="display: ${orderColumnDisplay};">
                                    <span class="order-badge" style="background: ${this.getOrderColor(orderIndex)};">${order.orderName}</span>
                                </td>
                                <td><input type="text" value="${item.description || ''}" class="item-description" title="项目描述 / Item description" readonly></td>
                                <td><input type="number" value="${item.quantity || 1}" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity" readonly></td>
                                <td><input type="number" value="${item.price || 0}" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price" readonly></td>
                                <td class="item-amount">${formatCurrency(item.amount || 0)}</td>
                                <td>
                                    <span class="order-info" style="font-size: 12px; color: #666;">
                                        ${order.customerName || '未知客户'}
                                    </span>
                                </td>
                            `;

                            tbody.appendChild(row);
                            totalAmount += item.amount || 0;
                            totalItems++;
                        });
                    }
                });

                // 使用统一的总金额计算和更新
                updateTotalAmount();

                // 生成并更新合并的单据号码
                this.updateCombinedDocumentNumber();
                const combinedDocumentNumber = this.generateCombinedDocumentNumber();

                // 更新当前订单信息显示
                const orderDisplay = document.getElementById('current-order-display');
                const customerDisplay = document.getElementById('current-customer-display');

                if (orderDisplay) {
                    orderDisplay.textContent = combinedDocumentNumber || `合并视图 (${AppConfig.multiOrderData.length}个订单)`;
                }
                if (customerDisplay) {
                    customerDisplay.textContent = `${totalItems}个项目`;
                }

                // 获取最终计算的总金额用于日志
                const finalTotalAmount = calculateTotalAmount();
                console.log(`✅ 合并显示完成: ${AppConfig.multiOrderData.length}个订单, ${totalItems}个项目, 总金额: ${formatCurrency(finalTotalAmount)}`);
                console.log(`📋 合并单据号码: ${combinedDocumentNumber}`);
            },

            /**
             * 显示分别视图
             * @function showSeparateView - 只显示当前选中订单的项目
             */
            showSeparateView() {
                console.log('🔄 切换到分别显示模式');

                if (AppConfig.multiOrderData.length === 0) {
                    console.warn('⚠️ 没有订单数据可显示');
                    return;
                }

                // 移除合并视图样式
                const table = document.getElementById('items-table');
                table.classList.remove('combined-view');

                // 加载当前订单到表单
                const currentOrder = AppConfig.multiOrderData[AppConfig.currentOrderIndex];
                if (currentOrder) {
                    this.loadOrderToForm(currentOrder);
                    this.updateCurrentOrderDisplay();

                    // 恢复当前订单的独立单据号码
                    const documentNumberInput = document.getElementById('document-number');
                    if (documentNumberInput && currentOrder.documentNumber) {
                        documentNumberInput.value = currentOrder.documentNumber;
                        console.log(`📋 恢复独立单据号码: ${currentOrder.documentNumber}`);
                    }

                    // 更新总金额显示
                    updateTotalAmount();

                    console.log(`✅ 分别显示完成: 当前显示订单 ${currentOrder.orderName}`);
                } else {
                    console.warn('⚠️ 当前订单索引无效:', AppConfig.currentOrderIndex);
                }
            },

            /**
             * 获取订单颜色
             * @function getOrderColor - 为不同订单分配不同的颜色
             * @param {number} orderIndex - 订单索引
             * @returns {string} 颜色值
             */
            getOrderColor(orderIndex) {
                const colors = [
                    '#007bff', // 蓝色
                    '#28a745', // 绿色
                    '#dc3545', // 红色
                    '#ffc107', // 黄色
                    '#6f42c1', // 紫色
                    '#fd7e14', // 橙色
                    '#20c997', // 青色
                    '#e83e8c'  // 粉色
                ];
                return colors[orderIndex % colors.length];
            },

            /**
             * 生成合并的单据号码
             * @function generateCombinedDocumentNumber - 将多个订单的单据号码合并为叠加形态
             * @returns {string} 合并后的单据号码
             */
            generateCombinedDocumentNumber() {
                if (AppConfig.multiOrderData.length === 0) {
                    return '';
                }

                const documentNumbers = AppConfig.multiOrderData
                    .map(order => order.documentNumber)
                    .filter(number => number && number.trim() !== '') // 过滤空值
                    .map(number => number.trim()); // 去除空格

                if (documentNumbers.length === 0) {
                    return '';
                }

                const combinedNumber = documentNumbers.join('/');
                console.log(`📋 生成合并单据号码: ${combinedNumber} (来自${documentNumbers.length}个订单)`);
                return combinedNumber;
            },

            /**
             * 更新合并模式下的单据号码显示
             * @function updateCombinedDocumentNumber - 更新合并模式下的单据号码字段
             */
            updateCombinedDocumentNumber() {
                if (AppConfig.displayMode === 'combined' && AppConfig.multiOrderMode) {
                    const combinedNumber = this.generateCombinedDocumentNumber();
                    const documentNumberInput = document.getElementById('document-number');

                    if (documentNumberInput && combinedNumber) {
                        documentNumberInput.value = combinedNumber;
                        console.log(`✅ 合并单据号码已更新: ${combinedNumber}`);
                    }
                }
            }
        };

        // #endregion

        // #region 项目管理功能
        /**
         * 添加新项目行
         * @function addItem - 在项目表格中添加新的项目行
         */
        function addItem() {
            const tbody = document.getElementById('items-tbody');
            const newRow = document.createElement('tr');

            // 根据多订单模式决定是否显示订单列
            const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';
            const currentOrderName = AppConfig.multiOrderMode && AppConfig.multiOrderData[AppConfig.currentOrderIndex]
                ? AppConfig.multiOrderData[AppConfig.currentOrderIndex].orderName
                : '';

            newRow.innerHTML = `
                <td class="order-column" style="display: ${orderColumnDisplay};">
                    <span class="order-badge">${currentOrderName}</span>
                </td>
                <td><input type="text" placeholder="项目描述 / Item description" class="item-description" title="项目描述 / Item description"></td>
                <td><input type="number" value="1" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                <td><input type="number" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                <td class="item-amount">0.00</td>
                <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
            `;

            tbody.appendChild(newRow);

            // 为新行添加事件监听器
            const quantityInput = newRow.querySelector('.item-quantity');
            const priceInput = newRow.querySelector('.item-price');
            const descInput = newRow.querySelector('.item-description');

            quantityInput.addEventListener('input', () => updateItemAmount(newRow));
            priceInput.addEventListener('input', () => updateItemAmount(newRow));

            // 为描述输入框添加自动预览更新
            if (AppConfig.autoPreview) {
                descInput.addEventListener('input', () => safeDebouncedUpdatePreview());
            }

            AppConfig.itemCounter++;
        }

        /**
         * 切换多订单模式
         * @function toggleMultiOrderMode - 切换单订单/多订单模式
         */
        function toggleMultiOrderMode() {
            MultiOrderManager.toggleMode();
        }

        /**
         * 添加新订单
         * @function addNewOrder - 添加新的订单
         */
        function addNewOrder() {
            if (!AppConfig.multiOrderMode) {
                console.warn('⚠️ 请先切换到多订单模式');
                return;
            }

            // 保存当前订单数据
            if (AppConfig.multiOrderData[AppConfig.currentOrderIndex]) {
                MultiOrderManager.saveCurrentOrderData();
            }

            // 创建新订单
            const newOrder = MultiOrderManager.createOrderFromCurrentForm();
            AppConfig.currentOrderIndex = AppConfig.multiOrderData.length - 1;

            // 清空表单准备输入新订单
            clearFormForNewOrder();

            // 更新界面
            MultiOrderManager.updateOrderTabs();
            MultiOrderManager.updateCurrentOrderDisplay();

            console.log('✅ 已添加新订单:', newOrder.orderId);
        }

        /**
         * 切换显示模式
         * @function switchDisplayMode - 切换分别显示/合并显示模式
         * @param {string} mode - 显示模式 ('separate' | 'combined')
         */
        function switchDisplayMode(mode) {
            if (!AppConfig.multiOrderMode) {
                console.warn('⚠️ 请先切换到多订单模式');
                return;
            }

            if (!mode || (mode !== 'separate' && mode !== 'combined')) {
                console.error('❌ 无效的显示模式:', mode);
                return;
            }

            try {
                AppConfig.displayMode = mode;

                if (mode === 'combined') {
                    MultiOrderManager.showCombinedView();
                    console.log('🔄 已切换到合并显示模式，单据号码已合并');
                } else {
                    MultiOrderManager.showSeparateView();
                    console.log('🔄 已切换到分别显示模式，单据号码已恢复独立');
                }

                // 更新显示模式选择器
                const displayModeSelector = document.getElementById('display-mode');
                if (displayModeSelector && displayModeSelector.value !== mode) {
                    displayModeSelector.value = mode;
                }

                console.log(`🔄 已切换到${mode === 'combined' ? '合并' : '分别'}显示模式`);
            } catch (error) {
                console.error('❌ 切换显示模式失败:', error);
            }
        }

        /**
         * 清空表单准备新订单
         * @function clearFormForNewOrder - 清空表单但保留公司信息
         */
        function clearFormForNewOrder() {
            // 清空客户信息
            document.getElementById('customer-name').value = '';
            document.getElementById('customer-phone').value = '';
            document.getElementById('customer-email').value = '';
            document.getElementById('notes').value = '';

            // 生成新的单据号码
            const docType = document.getElementById('document-type').value;
            document.getElementById('document-number').value = generateDocumentNumber(docType);

            // 清空项目表格，保留一行
            const tbody = document.getElementById('items-tbody');
            const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';

            tbody.innerHTML = `
                <tr>
                    <td class="order-column" style="display: ${orderColumnDisplay};"></td>
                    <td><input type="text" placeholder="项目描述 / Item description" class="item-description" title="项目描述 / Item description"></td>
                    <td><input type="number" value="1" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                    <td><input type="number" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                    <td class="item-amount">0.00</td>
                    <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                </tr>
            `;

            // 重新绑定事件
            initializeItemEvents();
            updateTotalAmount();
        }

        /**
         * 删除项目行
         * @function removeItem - 删除指定的项目行
         * @param {HTMLElement} button - 删除按钮元素
         */
        function removeItem(button) {
            const row = button.closest('tr');
            const tbody = document.getElementById('items-tbody');

            // 确保至少保留一行
            if (tbody.children.length > 1) {
                row.remove();
                updateTotalAmount();
            } else {
                // 如果只剩一行，清空内容而不删除
                row.querySelector('.item-description').value = '';
                row.querySelector('.item-quantity').value = '1';
                row.querySelector('.item-price').value = '';
                updateItemAmount(row);
            }
        }

        /**
         * 清空表单
         * @function clearForm - 清空所有表单输入
         */
        function clearForm() {
            // 重置基本信息
            if (DOMCache.documentNumber) DOMCache.documentNumber.value = '';
            if (DOMCache.documentDate) DOMCache.documentDate.value = '';

            // 重置公司信息
            const companyFields = ['company-name', 'tax-id', 'company-address', 'company-phone', 'contact-person'];
            companyFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) element.value = '';
            });

            // 重置客户信息
            if (DOMCache.customerName) DOMCache.customerName.value = '';
            if (DOMCache.channel) DOMCache.channel.value = '';
            if (DOMCache.customerPhone) DOMCache.customerPhone.value = '';
            if (DOMCache.customerEmail) DOMCache.customerEmail.value = '';
            if (DOMCache.notes) DOMCache.notes.value = '';

            // 清空项目表格，只保留一行
            const tbody = DOMCache.itemsTbody || document.getElementById('items-tbody');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td><input type="text" placeholder="项目描述 / Item description" class="item-description" title="项目描述 / Item description"></td>
                        <td><input type="number" value="1" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                        <td><input type="number" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                        <td class="item-amount">0.00</td>
                        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                    </tr>
                `;
            }

            // 重新绑定事件
            initializeItemEvents();

            // 更新总金额
            updateTotalAmount();

            // 清空预览
            const documentContainer = SafeDOM.get('document-container');
            if (documentContainer) {
                const emptyMessage = SafeDOMBuilder.createEmptyMessage(
                    '请填写表单信息并点击"更新预览"按钮\nPlease fill in the form and click "Update Preview" button'
                );
                SafeDOMBuilder.safeReplaceContent(documentContainer, emptyMessage);
            }

            // 重新生成单据号码
            const docType = document.getElementById('document-type').value;
            document.getElementById('document-number').value = generateDocumentNumber(docType);
        }

        /**
         * 初始化项目输入事件
         * @function initializeItemEvents - 为项目输入框绑定事件监听器
         */
        function initializeItemEvents() {
            const rows = document.querySelectorAll('#items-tbody tr');

            rows.forEach(row => {
                const quantityInput = row.querySelector('.item-quantity');
                const priceInput = row.querySelector('.item-price');
                const descInput = row.querySelector('.item-description');

                quantityInput.addEventListener('input', () => updateItemAmount(row));
                priceInput.addEventListener('input', () => updateItemAmount(row));

                // 为描述输入框添加自动预览更新
                if (AppConfig.autoPreview) {
                    descInput.addEventListener('input', () => safeDebouncedUpdatePreview());
                }
            });
        }
        // #endregion

        // #region 模板渲染功能
        /**
         * 发票模板类 - 简化版
         * @description 处理发票文档的渲染逻辑
         */
        class InvoiceTemplate {
            /**
             * 渲染发票模板
             * @function render - 根据数据渲染发票
             * @param {Object} data - 发票数据
             * @param {boolean} isExport - 是否为导出模式
             * @returns {string} 渲染后的HTML字符串
             */
            static render(data, isExport = false) {
                const company = AppConfig.currentCompany;
                const companyInfo = CompanyInfo[company];

                return `
                    <div class="invoice-container">
                        ${this.renderHeader(company, companyInfo, data, isExport)}
                        ${this.renderTitle('发票 / INVOICE', data.documentNumber, data.date)}
                        ${this.renderCompanyInfo(data)}
                        ${this.renderCustomerInfo(data)}
                        ${this.renderItemsTable(data.items)}
                        ${this.renderTotal(data.total)}
                        ${this.renderNotes(data.notes)}
                        ${this.renderElectronicSignature()}
                        ${this.renderFooter(company, isExport)}
                        ${this.renderStamp(company, isExport)}
                    </div>
                `;
            }

            /**
             * 渲染页眉
             * @function renderHeader - 渲染文档页眉
             * @param {string} company - 公司代码
             * @param {Object} companyInfo - 公司信息
             * @param {Object} data - 表单数据
             * @param {boolean} isExport - 是否为导出模式
             * @returns {string} 页眉HTML
             */
            static renderHeader(company, companyInfo, data, isExport = false) {
                const headerImage = ImageBase64.getHeader(company);
                const logo = ImageBase64.getLogo(company);

                console.log(`📋 渲染页眉 - 公司: ${company}, 导出模式: ${isExport}, 页眉图片: ${!!headerImage}, 标志: ${!!logo}`);

                // 如果有页眉图片，直接显示
                if (headerImage) {
                    return `
                        <div class="document-header-image-container">
                            <img src="${headerImage}" alt="页眉图片">
                        </div>
                    `;
                }

                // 导出模式下，如果没有页眉图片则返回空字符串，完全避免任何占位符
                if (isExport) {
                    console.log(`📋 导出模式 - ${company}公司无页眉图片和logo，返回完全空内容`);
                    return '';
                }

                // 预览模式下显示图片占位区域（包含logo处理）
                console.log(`📋 预览模式 - 显示页眉占位符，logo状态: ${!!logo}`);
                return `
                    <div class="document-header">
                        <div class="image-placeholder header-placeholder">
                            页眉图片区域 / Header Image Area<br>
                            <small style="font-size: 10px; opacity: 0.7;">建议尺寸: 794x90px / Recommended: 794x90px</small>
                            ${logo ? `<br><img src="${logo}" alt="公司标志" style="max-height: 40px; margin-top: 5px;">` : '<br><small style="font-size: 10px;">公司标志占位 / Logo Placeholder</small>'}
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染标题
             * @function renderTitle - 渲染文档标题
             * @param {string} title - 文档标题
             * @param {string} number - 单据号码
             * @param {string} date - 日期
             * @returns {string} 标题HTML
             */
            static renderTitle(title, number, date) {
                return `
                    <div style="text-align: center; margin: 15px 0; padding: 0 10px; box-sizing: border-box; clear: both;">
                        <h1 style="color: var(--primary-color); margin-bottom: 8px; font-size: 22px; line-height: 1.3; word-wrap: break-word; overflow-wrap: break-word; -webkit-hyphens: auto; hyphens: auto; box-sizing: border-box;">${title}</h1>
                        <div style="display: flex; justify-content: space-between; margin-top: 12px; font-size: 13px; flex-wrap: wrap; gap: 10px; line-height: 1.4; box-sizing: border-box;">
                            <div style="flex: 1; min-width: 200px; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box;"><strong>单据号码 / Document Number:</strong> ${number}</div>
                            <div style="flex: 1; min-width: 150px; text-align: right; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box;"><strong>日期 / Date:</strong> ${date}</div>
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染公司信息
             * @function renderCompanyInfo - 渲染公司信息（智能显示）
             * @param {Object} data - 表单数据
             * @returns {string} 公司信息HTML
             */
            static renderCompanyInfo(data) {
                const fields = [];

                // 使用安全版本的数据，如果不存在则对原始数据进行转义
                if (data.companyName) {
                    const safeCompanyName = data.safeCompanyName || escapeHtml(data.companyName);
                    fields.push(`<div><strong>公司名称 / Company Name:</strong> ${safeCompanyName}</div>`);
                }
                if (data.taxId) {
                    const safeTaxId = data.safeTaxId || escapeHtml(data.taxId);
                    fields.push(`<div><strong>税号 / Tax ID:</strong> ${safeTaxId}</div>`);
                }
                if (data.companyAddress) {
                    const safeCompanyAddress = data.safeCompanyAddress || escapeHtml(data.companyAddress);
                    fields.push(`<div><strong>公司地址 / Company Address:</strong> ${safeCompanyAddress}</div>`);
                }
                if (data.companyPhone) {
                    const safeCompanyPhone = data.safeCompanyPhone || escapeHtml(data.companyPhone);
                    fields.push(`<div><strong>公司电话 / Company Phone:</strong> ${safeCompanyPhone}</div>`);
                }
                if (data.contactPerson) {
                    const safeContactPerson = data.safeContactPerson || escapeHtml(data.contactPerson);
                    fields.push(`<div><strong>负责人 / Contact Person:</strong> ${safeContactPerson}</div>`);
                }

                if (fields.length === 0) return '';

                return `
                    <div style="margin: 12px 0; padding: 10px; border: 1px solid var(--border-color); border-radius: 6px; background: #f9f9f9; box-sizing: border-box; clear: both;">
                        <h3 style="margin-top: 0; margin-bottom: 8px; color: var(--dark-color); font-size: 15px; line-height: 1.3; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box;">公司信息 / Company Information</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 12px; line-height: 1.5; word-wrap: break-word; overflow-wrap: break-word; -webkit-hyphens: auto; hyphens: auto; box-sizing: border-box;">
                            ${fields.join('')}
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染客户信息
             * @function renderCustomerInfo - 渲染客户信息（智能显示）
             * @param {Object} data - 客户数据
             * @returns {string} 客户信息HTML
             */
            static renderCustomerInfo(data) {
                const fields = [];

                // 使用安全版本的数据，如果不存在则对原始数据进行转义
                if (data.customerName) {
                    const safeCustomerName = data.safeCustomerName || escapeHtml(data.customerName);
                    fields.push(`<div><strong>客户名称 / Customer Name:</strong> ${safeCustomerName}</div>`);
                }
                if (data.channel) {
                    const safeChannel = data.safeChannel || escapeHtml(data.channel);
                    fields.push(`<div><strong>渠道 / Channel:</strong> ${safeChannel}</div>`);
                }
                if (data.customerPhone) {
                    const safeCustomerPhone = data.safeCustomerPhone || escapeHtml(data.customerPhone);
                    fields.push(`<div><strong>客户电话 / Customer Phone:</strong> ${safeCustomerPhone}</div>`);
                }
                if (data.customerEmail) {
                    const safeCustomerEmail = data.safeCustomerEmail || escapeHtml(data.customerEmail);
                    fields.push(`<div><strong>客户邮箱 / Customer Email:</strong> ${safeCustomerEmail}</div>`);
                }

                if (fields.length === 0) return '';

                return `
                    <div style="margin: 12px 0; padding: 10px; border: 1px solid var(--border-color); border-radius: 6px; box-sizing: border-box; clear: both;">
                        <h3 style="margin-top: 0; margin-bottom: 8px; color: var(--dark-color); font-size: 15px; line-height: 1.3; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box;">客户信息 / Customer Information</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 12px; line-height: 1.5; word-wrap: break-word; overflow-wrap: break-word; -webkit-hyphens: auto; hyphens: auto; box-sizing: border-box;">
                            ${fields.join('')}
                        </div>
                    </div>
                `;
            }
            /**
             * 渲染项目表格
             * @function renderItemsTable - 渲染项目明细表格（响应式字体）
             * @param {Array} items - 项目数组
             * @returns {string} 项目表格HTML
             */
            static renderItemsTable(items) {
                if (items.length === 0) return '';

                // 根据项目数量调整字体大小
                const fontSize = items.length > 10 ? '11px' : items.length > 5 ? '12px' : '13px';
                const padding = items.length > 10 ? '6px' : items.length > 5 ? '8px' : '10px';

                const currencySymbol = getCurrentCurrencySymbol();
                const tableRows = items.map((item, index) => {
                    // 使用安全版本的描述，如果不存在则对原始数据进行转义
                    const safeDescription = item.safeDescription || escapeHtml(item.description);

                    return `
                        <tr>
                            <td style="text-align: center; padding: ${padding}; border: 1px solid var(--border-color); word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.4;">${index + 1}</td>
                            <td style="padding: ${padding}; border: 1px solid var(--border-color); word-wrap: break-word; overflow-wrap: break-word; -webkit-hyphens: auto; hyphens: auto; box-sizing: border-box; vertical-align: top; line-height: 1.4; white-space: normal;">${safeDescription}</td>
                            <td style="text-align: center; padding: ${padding}; border: 1px solid var(--border-color); word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.4;">${item.quantity}</td>
                            <td style="text-align: right; padding: ${padding}; border: 1px solid var(--border-color); word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.4; white-space: nowrap;">${currencySymbol} ${formatCurrency(item.price)}</td>
                            <td style="text-align: right; padding: ${padding}; border: 1px solid var(--border-color); word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.4; white-space: nowrap;">${currencySymbol} ${formatCurrency(item.amount)}</td>
                        </tr>
                    `;
                }).join('');

                return `
                    <div style="margin: 12px 0; padding: 0 10px; box-sizing: border-box; clear: both;">
                        <h3 style="color: var(--dark-color); font-size: 15px; margin-bottom: 6px; line-height: 1.3; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box;">项目明细 / Item Details</h3>
                        <table style="width: 100%; border-collapse: collapse; font-size: ${fontSize}; table-layout: fixed; box-sizing: border-box; border-spacing: 0;">
                            <thead>
                                <tr style="background-color: var(--light-color);">
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: center; width: 8%; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.3;">序号<br/>No.</th>
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: left; width: 40%; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.3;">项目描述<br/>Description</th>
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: center; width: 12%; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.3;">数量<br/>Qty</th>
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: right; width: 20%; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.3;">单价<br/>Unit Price</th>
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: right; width: 20%; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.3;">金额<br/>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${tableRows}
                            </tbody>
                        </table>
                    </div>
                `;
            }

            /**
             * 渲染总金额
             * @function renderTotal - 渲染总金额信息
             * @param {number} total - 总金额
             * @returns {string} 总金额HTML
             */
            static renderTotal(total) {
                const currencySymbol = getCurrentCurrencySymbol();

                // 详细调试信息
                console.log(`💰 渲染总金额开始 - 输入参数:`, {
                    原始total: total,
                    类型: typeof total,
                    是否为数字: !isNaN(total),
                    货币符号: currencySymbol,
                    当前货币: AppConfig.currentCurrency,
                    多订单模式: AppConfig.multiOrderMode,
                    显示模式: AppConfig.displayMode
                });

                // 确保总金额有效
                let safeTotal = 0;
                if (total !== null && total !== undefined && !isNaN(total)) {
                    safeTotal = parseFloat(total);
                } else {
                    console.warn(`⚠️ 总金额无效，使用默认值0 - 原始值: ${total}`);
                }

                const displayTotal = formatCurrency(safeTotal);

                console.log(`💰 渲染总金额完成 - 最终显示:`, {
                    安全total: safeTotal,
                    格式化结果: displayTotal,
                    完整显示: `${currencySymbol} ${displayTotal}`,
                    总金额层级: '200 (固定数值)',
                    印章层级: '30 (CSS变量)',
                    层级说明: '总金额层级(200) > 印章层级(30)，确保总金额在上层',
                    CSS修复: '使用不透明背景色，避免导出时出现灰色蒙板'
                });

                return `
                    <div style="margin: 15px 0; text-align: right; position: relative; z-index: 200; clear: both; box-sizing: border-box;">
                        <div class="total-amount-container" style="display: inline-block; padding: 12px 18px; border: 2px solid #1e40af; border-radius: 6px; background-color: #ffffff; box-shadow: 0 2px 4px rgba(0,0,0,0.1); min-width: 200px; max-width: 100%; z-index: 200; box-sizing: border-box; position: relative;">
                            <h3 style="margin: 0; color: #1e40af; font-size: 16px; font-weight: bold; line-height: 1.4; text-shadow: none; word-wrap: break-word; overflow-wrap: break-word; position: relative; z-index: 201; box-sizing: border-box; white-space: nowrap;">总金额 / Total Amount: ${currencySymbol} ${displayTotal}</h3>
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染备注
             * @function renderNotes - 渲染备注信息
             * @param {string} notes - 备注内容
             * @returns {string} 备注HTML
             */
            static renderNotes(notes) {
                if (!notes) return '';

                // 如果传入的是数据对象，使用安全版本；否则直接转义
                const safeNotes = (typeof notes === 'object' && notes.safeNotes)
                    ? notes.safeNotes
                    : escapeHtml(notes);

                return `
                    <div style="margin: 12px 0; padding: 0 10px; box-sizing: border-box; clear: both;">
                        <h3 style="color: var(--dark-color); font-size: 15px; margin-bottom: 6px; line-height: 1.3; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box;">备注 / Notes</h3>
                        <div style="padding: 10px; border: 1px solid var(--border-color); border-radius: 6px; background-color: #f9f9f9; font-size: 12px; line-height: 1.5; word-wrap: break-word; overflow-wrap: break-word; -webkit-hyphens: auto; hyphens: auto; box-sizing: border-box; white-space: pre-wrap;">
                            ${safeNotes}
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染电子生成标识
             * @function renderElectronicSignature - 渲染电子生成提示
             * @returns {string} 电子生成标识HTML
             */
            static renderElectronicSignature() {
                return `
                    <div class="electronic-signature">
                        此文档为电子生成 / This document is electronically generated
                    </div>
                `;
            }

            /**
             * 渲染页脚
             * @function renderFooter - 渲染文档页脚（带占位区域）
             * @param {string} company - 公司代码
             * @param {boolean} isExport - 是否为导出模式
             * @returns {string} 页脚HTML
             */
            static renderFooter(company, isExport = false) {
                const footerImage = ImageBase64.getFooter(company);

                if (footerImage) {
                    return `
                        <div class="unified-document-footer company-footer-image-container">
                            <img src="${footerImage}" alt="页脚图片">
                        </div>
                    `;
                }

                // 导出模式下，如果没有图片则返回空字符串
                if (isExport) {
                    return '';
                }

                // 预览模式下显示图片占位区域
                return `
                    <div class="document-footer">
                        <div class="image-placeholder footer-placeholder">
                            页脚图片区域 / Footer Image Area<br>
                            <small style="font-size: 10px; opacity: 0.7;">建议尺寸: 794x70px / Recommended: 794x70px</small>
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染印章
             * @function renderStamp - 渲染公司印章（带占位区域）
             * @param {string} company - 公司代码
             * @param {boolean} isExport - 是否为导出模式
             * @returns {string} 印章HTML
             */
            static renderStamp(company, isExport = false) {
                const stamp = ImageBase64.getStamp(company);

                if (stamp) {
                    return `
                        <div class="company-stamp">
                            <img src="${stamp}" alt="公司印章">
                        </div>
                    `;
                }

                // 导出模式下，如果没有图片则返回空字符串
                if (isExport) {
                    return '';
                }

                // 预览模式下显示图片占位区域
                return `
                    <div class="image-placeholder stamp-placeholder">
                        印章区域<br>Stamp Area<br>
                        <small style="font-size: 9px; opacity: 0.7;">120x120px</small>
                    </div>
                `;
            }
        }

        /**
         * 收据模板类
         * @description 处理收据文档的渲染逻辑
         */
        class ReceiptTemplate {
            /**
             * 渲染收据模板
             * @function render - 根据数据渲染收据
             * @param {Object} data - 收据数据
             * @param {boolean} isExport - 是否为导出模式
             * @returns {string} 渲染后的HTML字符串
             */
            static render(data, isExport = false) {
                const company = AppConfig.currentCompany;
                const companyInfo = CompanyInfo[company];

                return `
                    <div class="receipt-container">
                        ${InvoiceTemplate.renderHeader(company, companyInfo, data, isExport)}
                        ${InvoiceTemplate.renderTitle('收据 / RECEIPT', data.documentNumber, data.date)}
                        ${InvoiceTemplate.renderCompanyInfo(data)}
                        ${this.renderReceiptInfo(data)}
                        ${InvoiceTemplate.renderItemsTable(data.items)}
                        ${InvoiceTemplate.renderTotal(data.total)}
                        ${InvoiceTemplate.renderNotes(data.notes)}
                        ${InvoiceTemplate.renderElectronicSignature()}
                        ${InvoiceTemplate.renderFooter(company, isExport)}
                        ${InvoiceTemplate.renderStamp(company, isExport)}
                    </div>
                `;
            }

            /**
             * 渲染收据信息
             * @function renderReceiptInfo - 渲染收据特有信息（智能显示）
             * @param {Object} data - 收据数据
             * @returns {string} 收据信息HTML
             */
            static renderReceiptInfo(data) {
                const fields = [];

                // 使用安全版本的数据，如果不存在则对原始数据进行转义
                if (data.customerName) {
                    const safeCustomerName = data.safeCustomerName || escapeHtml(data.customerName);
                    fields.push(`<div><strong>付款人 / Payer:</strong> ${safeCustomerName}</div>`);
                }
                if (data.channel) {
                    const safeChannel = data.safeChannel || escapeHtml(data.channel);
                    fields.push(`<div><strong>渠道 / Channel:</strong> ${safeChannel}</div>`);
                }
                if (data.customerPhone) {
                    const safeCustomerPhone = data.safeCustomerPhone || escapeHtml(data.customerPhone);
                    fields.push(`<div><strong>联系方式 / Contact:</strong> ${safeCustomerPhone}</div>`);
                }
                if (data.customerEmail) {
                    const safeCustomerEmail = data.safeCustomerEmail || escapeHtml(data.customerEmail);
                    fields.push(`<div><strong>邮箱 / Email:</strong> ${safeCustomerEmail}</div>`);
                }

                if (fields.length === 0) return '';

                return `
                    <div style="margin: 12px 0; padding: 10px; border: 1px solid var(--border-color); border-radius: 6px; box-sizing: border-box;">
                        <h3 style="margin-top: 0; margin-bottom: 8px; color: var(--dark-color); font-size: 15px; line-height: 1.3;">收款信息 / Payment Information</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 6px; font-size: 12px; line-height: 1.4; word-wrap: break-word;">
                            ${fields.join('')}
                        </div>
                        <div style="margin-top: 10px; padding: 8px; background-color: var(--light-color); border-radius: 4px; font-size: 12px; line-height: 1.4;">
                            <strong>收款确认 / Payment Confirmation:</strong> 已收到上述款项，特此开具收据。/ The above payment has been received. This receipt is issued accordingly.
                        </div>
                    </div>
                `;
            }
        }
        // #endregion

        // #region 主要功能函数
        // 预览更新重试计数器
        let previewUpdateRetryCount = 0;
        const MAX_PREVIEW_RETRY = 3;

        /**
         * 显示预览状态
         * @function showPreviewStatus - 显示预览更新状态
         * @param {string} message - 状态消息
         * @param {string} type - 状态类型 ('updating', 'success', 'error')
         */
        function showPreviewStatus(message, type = 'updating') {
            const statusElement = document.getElementById('preview-status');
            const statusText = document.getElementById('preview-status-text');

            if (statusElement && statusText) {
                statusText.textContent = message;

                // 重置类名并添加新的状态类
                statusElement.className = `preview-status-indicator ${type}`;

                // 显示或隐藏状态元素
                if (message.trim() === '') {
                    statusElement.classList.add('hidden');
                } else {
                    statusElement.classList.remove('hidden');
                }

                // 自动隐藏成功和错误状态（但不隐藏ready状态）
                if (type === 'success' || type === 'error') {
                    setTimeout(() => {
                        statusElement.classList.add('hidden');
                    }, 3000);
                }
            }
        }

        /**
         * 隐藏预览状态
         * @function hidePreviewStatus - 隐藏预览状态指示器
         */
        function hidePreviewStatus() {
            const statusElement = document.getElementById('preview-status');
            if (statusElement) {
                statusElement.classList.add('hidden');
            }
        }

        /**
         * 检查A4内容溢出
         * @function checkA4ContentOverflow - 检查内容是否超出A4边界
         */
        function checkA4ContentOverflow() {
            const documentContainer = document.getElementById('document-container');
            const documentPreview = document.getElementById('document-preview');

            if (!documentContainer || !documentPreview) {
                return false;
            }

            // 获取实际内容高度（不包括缩放）
            const containerHeight = documentContainer.scrollHeight;
            const a4Height = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--a4-height-px'));

            console.log(`📏 A4内容溢出检查:`, {
                容器实际高度: containerHeight,
                A4标准高度: a4Height,
                是否溢出: containerHeight > a4Height,
                溢出量: Math.max(0, containerHeight - a4Height)
            });

            // 如果内容高度超过A4高度，显示警告
            if (containerHeight > a4Height) {
                console.warn(`⚠️ 内容溢出A4边界: ${containerHeight - a4Height}px`);
                showContentOverflowWarning(containerHeight - a4Height);
                return true;
            } else {
                hideContentOverflowWarning();
                return false;
            }
        }

        /**
         * 显示内容溢出警告
         * @function showContentOverflowWarning - 显示内容溢出警告
         * @param {number} overflowAmount - 溢出的像素数
         */
        function showContentOverflowWarning(overflowAmount) {
            let warningElement = document.querySelector('.content-overflow-warning');

            if (!warningElement) {
                warningElement = document.createElement('div');
                warningElement.className = 'content-overflow-warning';
                document.getElementById('document-preview').appendChild(warningElement);
            }

            warningElement.textContent = `内容溢出A4边界 ${overflowAmount}px / Content overflows A4 boundary by ${overflowAmount}px`;
            warningElement.style.display = 'block';

            // 3秒后自动隐藏
            setTimeout(() => {
                warningElement.style.display = 'none';
            }, 3000);
        }

        /**
         * 隐藏内容溢出警告
         * @function hideContentOverflowWarning - 隐藏内容溢出警告
         */
        function hideContentOverflowWarning() {
            const warningElement = document.querySelector('.content-overflow-warning');
            if (warningElement) {
                warningElement.style.display = 'none';
            }
        }

        /**
         * 更新预览
         * @function updatePreview - 更新文档预览，支持发票和收据模板
         * @description 根据当前表单数据生成预览，支持多订单模式和实时更新
         * @note 此函数会被防抖机制调用，避免频繁更新影响性能
         * @see safeDebouncedUpdatePreview - 推荐使用的防抖版本
         */
        function updatePreview() {
            // 显示更新状态
            showPreviewStatus('更新预览中... / Updating preview...', 'updating');

            const data = collectFormData();

            console.log(`🔄 更新预览 - 数据收集:`, {
                项目数量: data.items.length,
                总金额原始值: data.total,
                总金额格式化: formatCurrency(data.total),
                货币符号: getCurrentCurrencySymbol(),
                文档类型: data.documentType,
                模式: AppConfig.multiOrderMode ? '多订单' : '单订单',
                显示模式: AppConfig.displayMode || '标准'
            });

            // 增强DOM元素检查和错误处理
            const container = document.getElementById('document-container');
            if (!container) {
                console.error('❌ 无法找到document-container元素，预览更新失败');

                // 防止无限循环：检查重试次数
                if (previewUpdateRetryCount >= MAX_PREVIEW_RETRY) {
                    console.error('❌ 预览容器修复重试次数已达上限，停止尝试');
                    previewUpdateRetryCount = 0; // 重置计数器
                    return;
                }

                console.log(`🔧 尝试重新创建预览容器结构 (第${previewUpdateRetryCount + 1}次尝试)`);

                // 尝试重新创建预览容器结构
                const previewContainer = document.getElementById('document-preview');
                if (previewContainer) {
                    previewContainer.innerHTML = `
                        <div id="document-container">
                            <div class="empty-preview-message">
                                请填写表单信息并点击"更新预览"按钮<br>
                                Please fill in the form and click "Update Preview" button
                            </div>
                        </div>
                    `;
                    console.log('✅ 预览容器结构已重新创建');

                    // 增加重试计数器并递归调用
                    previewUpdateRetryCount++;
                    setTimeout(() => updatePreview(), 100);
                    return;
                } else {
                    console.error('❌ document-preview元素也不存在，无法修复预览功能');
                    previewUpdateRetryCount = 0; // 重置计数器
                    return;
                }
            }

            // 成功找到容器，重置重试计数器
            previewUpdateRetryCount = 0;

            // 自动填充单据号码（如果为空）
            const documentNumberInput = document.getElementById('document-number');
            if (documentNumberInput && !documentNumberInput.value) {
                documentNumberInput.value = data.documentNumber;
            }

            // 自动填充日期（如果为空）
            const documentDateInput = document.getElementById('document-date');
            if (documentDateInput && !documentDateInput.value) {
                documentDateInput.value = data.date;
            }

            let html = '';

            try {
                if (data.documentType === 'invoice') {
                    html = InvoiceTemplate.render(data);
                } else {
                    html = ReceiptTemplate.render(data);
                }

                container.innerHTML = html;
                console.log('✅ 预览内容已更新到document-container');

                // 确保document-preview容器结构完整（不直接覆盖内容）
                const exportContainer = document.getElementById('document-preview');
                if (exportContainer && exportContainer !== container) {
                    // 检查document-preview是否包含document-container
                    const innerContainer = exportContainer.querySelector('#document-container');
                    if (!innerContainer) {
                        // 如果document-container不存在，重新创建结构
                        console.log('🔧 重新创建document-container结构');
                        exportContainer.innerHTML = `<div id="document-container">${html}</div>`;
                    }
                    console.log(`✅ 预览更新完成 - 保持了正确的容器结构`);
                } else {
                    console.log(`✅ 预览更新完成 - 使用统一容器`);
                }

                // 更新配置
                AppConfig.currentDocumentType = data.documentType;

                // 检查A4内容溢出（延迟执行以确保DOM更新完成）
                setTimeout(() => {
                    checkA4ContentOverflow();
                }, 100);

                // 显示成功状态
                showPreviewStatus('预览更新完成 / Preview updated', 'success');

            } catch (error) {
                console.error('❌ 预览渲染失败:', error);
                container.innerHTML = `
                    <div class="error-message" style="color: red; padding: 20px; text-align: center;">
                        预览渲染失败，请检查数据格式<br>
                        Preview rendering failed, please check data format<br>
                        <small>${error.message}</small>
                    </div>
                `;

                // 显示错误状态
                showPreviewStatus('预览更新失败 / Preview update failed', 'error');
            }
        }

        /**
         * 确保DOM完全就绪的辅助函数
         * @function ensureDOMReady - 确保DOM元素完全加载
         * @returns {Promise<boolean>} DOM是否就绪
         */
        async function ensureDOMReady() {
            return new Promise((resolve) => {
                if (document.readyState === 'complete') {
                    resolve(true);
                } else {
                    const checkReady = () => {
                        if (document.readyState === 'complete') {
                            resolve(true);
                        } else {
                            setTimeout(checkReady, 100);
                        }
                    };
                    checkReady();
                }
            });
        }

        // #region 导出函数已移至独立模块
        // 导出相关的兼容性函数已移至 export-components.js 模块
        // 包括：exportToPDF, exportToImage, debugImageExport
        // #endregion

        // #region 事件监听器和初始化
        /**
         * 检查外部依赖库是否加载成功
         * @function checkExternalDependencies - 检查外部库的可用性
         */
        function checkExternalDependencies() {
            const dependencies = {
                html2canvas: typeof html2canvas !== 'undefined',
                jsPDF: typeof window.jspdf !== 'undefined'
            };

            // 检查html2canvas的具体功能
            if (dependencies.html2canvas) {
                try {
                    // 测试html2canvas是否可以正常调用
                    const testElement = document.createElement('div');
                    testElement.style.width = '1px';
                    testElement.style.height = '1px';
                    testElement.style.position = 'absolute';
                    testElement.style.top = '-9999px';
                    document.body.appendChild(testElement);
                    
                    // 异步测试html2canvas基本功能
                    html2canvas(testElement, { scale: 1, logging: false })
                        .then(() => {
                            console.log('✅ html2canvas功能测试通过');
                            document.body.removeChild(testElement);
                        })
                        .catch((error) => {
                            console.error('❌ html2canvas功能测试失败:', error);
                            document.body.removeChild(testElement);
                        });
                } catch (error) {
                    console.error('❌ html2canvas测试异常:', error);
                    dependencies.html2canvas = false;
                }
            } else {
                console.warn('html2canvas未加载，图片导出功能将不可用');
            }

            if (!dependencies.jsPDF) {
                console.warn('jsPDF未加载，PDF导出功能将不可用');
            }

            return dependencies;
        }

        /**
         * 初始化默认预览显示
         * @function initializeDefaultPreview - 页面加载后自动显示默认预览
         */
        function initializeDefaultPreview() {
            console.log('🔄 初始化默认预览显示...');

            try {
                // 确保DOM元素已准备就绪
                setTimeout(() => {
                    // 检查预览容器是否存在
                    const container = document.getElementById('document-container');
                    if (!container) {
                        console.warn('⚠️ 预览容器未找到，跳过默认预览初始化');
                        return;
                    }

                    // 显示初始化状态
                    showPreviewStatus('正在生成默认预览... / Generating default preview...', 'updating');

                    // 收集当前表单的默认数据
                    const defaultData = collectFormData();

                    // 如果没有客户名称，设置一个默认示例
                    if (!defaultData.customerName || defaultData.customerName.trim() === '') {
                        defaultData.customerName = '示例客户 / Sample Customer';
                    }

                    // 如果没有项目，添加一个示例项目
                    if (!defaultData.items || defaultData.items.length === 0) {
                        defaultData.items = [{
                            description: '示例服务项目 / Sample Service Item',
                            quantity: 1,
                            price: 100.00,
                            amount: 100.00
                        }];
                        defaultData.total = 100.00;
                    }

                    // 生成预览HTML
                    let html = '';
                    if (defaultData.documentType === 'invoice') {
                        html = InvoiceTemplate.render(defaultData);
                    } else {
                        html = ReceiptTemplate.render(defaultData);
                    }

                    // 更新预览容器
                    container.innerHTML = html;

                    // 显示成功状态
                    showPreviewStatus('默认预览已加载 / Default preview loaded', 'success');

                    console.log('✅ 默认预览显示完成');

                    // 3秒后隐藏状态指示器
                    setTimeout(() => {
                        showPreviewStatus('', 'ready');
                    }, 3000);

                }, 100); // 短暂延迟确保DOM完全加载

            } catch (error) {
                console.error('❌ 默认预览初始化失败:', error);
                showPreviewStatus('预览初始化失败 / Preview initialization failed', 'error');
            }
        }

        /**
         * 初始化应用程序
         * @function initializeApp - 初始化所有事件监听器和默认值
         */
        function initializeApp() {
            // 检查外部依赖
            const dependencies = checkExternalDependencies();

            // 初始化DOM缓存
            DOMCache.initCache();

            // 设置默认日期
            const today = new Date().toISOString().split('T')[0];
            if (DOMCache.documentDate) {
                DOMCache.documentDate.value = today;
            }

            // 从localStorage恢复货币设置
            const savedCurrency = localStorage.getItem('smartoffice_currency');
            if (savedCurrency && CurrencyConfig[savedCurrency]) {
                AppConfig.currentCurrency = savedCurrency;
                if (DOMCache.currencySelector) {
                    DOMCache.currencySelector.value = savedCurrency;
                }
            }

            // 初始化货币显示
            updateAllCurrencyDisplays();

            // 初始化字段显示状态
            const initialDocType = DOMCache.documentType ? DOMCache.documentType.value : 'receipt';
            toggleCompanyFields(initialDocType);

            // 初始化项目事件
            initializeItemEvents();

            // 绑定表单变化事件（自动预览）
            const formElements = [
                'document-type',
                'company-selector',
                'currency-selector',
                'company-name',
                'tax-id',
                'company-address',
                'company-phone',
                'contact-person',
                'document-number',
                'document-date',
                'customer-name',
                'channel',
                'customer-phone',
                'customer-email',
                'notes'
            ];

            formElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', () => {
                        if (id === 'company-selector') {
                            AppConfig.currentCompany = element.value;
                        } else if (id === 'currency-selector') {
                            switchCurrency(element.value);
                        } else if (id === 'document-type') {
                            toggleCompanyFields(element.value);
                        }

                        // 自动预览更新（使用优化的防抖函数）
                        if (AppConfig.autoPreview) {
                            safeDebouncedUpdatePreview();
                        }
                    });

                    // 为文本输入框添加输入事件（使用优化的防抖函数）
                    if (element.type === 'text' || element.type === 'email' || element.type === 'tel' ||
                        element.type === 'number' || element.tagName === 'TEXTAREA') {
                        element.addEventListener('input', () => {
                            if (AppConfig.autoPreview) {
                                safeDebouncedUpdatePreview();
                            }
                        });

                        // 为数字输入框添加额外的change事件监听
                        if (element.type === 'number') {
                            element.addEventListener('change', () => {
                                if (AppConfig.autoPreview) {
                                    safeDebouncedUpdatePreview();
                                }
                            });
                        }
                    }
                }
            });

            // 绑定文档类型变化事件
            document.getElementById('document-type').addEventListener('change', function() {
                const newType = this.value;
                const numberInput = document.getElementById('document-number');

                // 如果单据号码为空或者是旧格式，生成新的单据号码
                if (!numberInput.value ||
                    (newType === 'invoice' && numberInput.value.startsWith('RCP')) ||
                    (newType === 'receipt' && numberInput.value.startsWith('INV'))) {
                    numberInput.value = generateDocumentNumber(newType);
                }
            });

            // 初始化单据号码
            const initialType = document.getElementById('document-type').value;
            document.getElementById('document-number').value = generateDocumentNumber(initialType);

            // 初始化默认预览显示
            initializeDefaultPreview();

            console.log('发票/收据生成器初始化完成 / Invoice/Receipt Generator Initialized');
        }

        /**
         * 页面加载完成后初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化事件管理器（优先初始化）
            EventManager.init();

            initializeApp();

            // 初始化现代化导出系统（已移至export-components.js模块）
            if (typeof ModernExportSystem !== 'undefined') {
                ModernExportSystem.init();
            }

            // 初始化调试管理器
            DebugManager.init();

            // 初始化图片质量管理器
            ImageQualityManager.init();

            // 显示欢迎信息
            console.log('='.repeat(50));
            console.log('发票/收据生成器 - SmartOffice 3.0');
            console.log('独立HTML版本 - 支持离线使用');
            console.log('功能: 发票生成、收据生成、300DPI高质量导出');
            console.log('新增: 现代化导出系统、无损图片处理、优化事件管理');
            console.log('事件绑定统计:', EventManager.getBindingStats());
            console.log('='.repeat(50));
        });

        /**
         * 全局错误处理
         */
        window.addEventListener('error', function(event) {
            console.error('发生错误:', event.error);
            // 可以在这里添加用户友好的错误提示
        });

        /**
         * 导出全局函数供HTML调用
         */
        window.addItem = addItem;
        window.removeItem = removeItem;
        window.clearForm = clearForm;
        /**
         * 增强的内容一致性诊断工具
         * @function debugContentConsistency - 诊断预览内容与表单数据的一致性问题
         */
        window.debugContentConsistency = function() {
            console.log('🔍 开始内容一致性诊断...');

            try {
                // 1. 收集表单数据
                const formData = collectFormData();
                console.log('📋 表单数据:', formData);

                // 2. 分析预览容器内容
                const container = document.getElementById('document-container');
                if (!container) {
                    console.error('❌ 预览容器不存在');
                    return { error: '预览容器不存在' };
                }

                // 3. 提取预览中的文字内容
                const previewText = {
                    customerName: extractTextFromPreview(container, '客户名称', '客户'),
                    documentNumber: extractTextFromPreview(container, '单据号码', 'Document Number'),
                    items: extractItemsFromPreview(container),
                    totalAmount: extractTotalFromPreview(container),
                    notes: extractNotesFromPreview(container)
                };

                console.log('🖼️ 预览内容:', previewText);

                // 4. 对比分析
                const comparison = {
                    customerName: {
                        form: formData.customerName,
                        preview: previewText.customerName,
                        match: formData.customerName === previewText.customerName
                    },
                    documentNumber: {
                        form: formData.documentNumber,
                        preview: previewText.documentNumber,
                        match: formData.documentNumber === previewText.documentNumber
                    },
                    itemsCount: {
                        form: formData.items.length,
                        preview: previewText.items.length,
                        match: formData.items.length === previewText.items.length
                    },
                    notes: {
                        form: formData.notes,
                        preview: previewText.notes,
                        match: formData.notes === previewText.notes
                    }
                };

                console.log('🔍 一致性对比:', comparison);

                // 5. 检查HTML转义问题
                const escapeTest = checkHtmlEscaping(formData);
                console.log('🔒 HTML转义检查:', escapeTest);

                // 6. 检查字符编码问题
                const encodingTest = checkCharacterEncoding(formData);
                console.log('🔤 字符编码检查:', encodingTest);

                return {
                    formData,
                    previewText,
                    comparison,
                    escapeTest,
                    encodingTest,
                    summary: generateDiagnosisSummary(comparison, escapeTest, encodingTest)
                };

            } catch (error) {
                console.error('❌ 诊断过程出错:', error);
                return { error: error.message };
            }
        };

        // debugImageExport 函数已移至 export-components.js 模块

        /**
         * 从预览中提取文字内容的辅助函数
         */
        function extractTextFromPreview(container, ...keywords) {
            try {
                for (const keyword of keywords) {
                    const elements = container.querySelectorAll('*');
                    for (const element of elements) {
                        if (element.textContent.includes(keyword)) {
                            const text = element.textContent;
                            const match = text.match(new RegExp(`${keyword}[：:]*\\s*([^\\n\\r]+)`));
                            if (match) {
                                return match[1].trim();
                            }
                        }
                    }
                }
                return '';
            } catch (error) {
                console.error('提取文字失败:', error);
                return '';
            }
        }

        function extractItemsFromPreview(container) {
            try {
                const rows = container.querySelectorAll('table tbody tr');
                return Array.from(rows).map(row => {
                    const cells = row.querySelectorAll('td');
                    return {
                        description: cells[1]?.textContent.trim() || '',
                        quantity: cells[2]?.textContent.trim() || '',
                        price: cells[3]?.textContent.trim() || '',
                        amount: cells[4]?.textContent.trim() || ''
                    };
                });
            } catch (error) {
                console.error('提取项目失败:', error);
                return [];
            }
        }

        function extractTotalFromPreview(container) {
            try {
                const totalElement = container.querySelector('.total-amount-container');
                return totalElement ? totalElement.textContent.trim() : '';
            } catch (error) {
                console.error('提取总金额失败:', error);
                return '';
            }
        }

        function extractNotesFromPreview(container) {
            try {
                const notesElements = container.querySelectorAll('*');
                for (const element of notesElements) {
                    if (element.textContent.includes('备注') || element.textContent.includes('Notes')) {
                        const parent = element.parentElement;
                        const notesDiv = parent?.querySelector('div[style*="pre-wrap"]');
                        return notesDiv ? notesDiv.textContent.trim() : '';
                    }
                }
                return '';
            } catch (error) {
                console.error('提取备注失败:', error);
                return '';
            }
        }

        function checkHtmlEscaping(data) {
            const results = {};
            const testStrings = ['<script>', '"test"', "'test'", '&amp;', '<>&"\''];

            for (const field in data) {
                if (typeof data[field] === 'string') {
                    results[field] = {
                        original: data[field],
                        escaped: escapeHtml(data[field]),
                        needsEscaping: testStrings.some(test => data[field].includes(test))
                    };
                }
            }

            return results;
        }

        function checkCharacterEncoding(data) {
            const results = {};

            for (const field in data) {
                if (typeof data[field] === 'string') {
                    const text = data[field];
                    results[field] = {
                        length: text.length,
                        byteLength: new Blob([text]).size,
                        hasChinese: /[\u4e00-\u9fff]/.test(text),
                        hasEmoji: /[\u{1f600}-\u{1f64f}]|[\u{1f300}-\u{1f5ff}]|[\u{1f680}-\u{1f6ff}]|[\u{1f1e0}-\u{1f1ff}]/u.test(text),
                        hasSpecialChars: /[^\x00-\x7F]/.test(text)
                    };
                }
            }

            return results;
        }

        function generateDiagnosisSummary(comparison, escapeTest, encodingTest) {
            const issues = [];

            // 检查一致性问题
            for (const field in comparison) {
                if (!comparison[field].match) {
                    issues.push(`${field}: 表单与预览内容不一致`);
                }
            }

            // 检查转义问题
            for (const field in escapeTest) {
                if (escapeTest[field].needsEscaping) {
                    issues.push(`${field}: 包含需要转义的特殊字符`);
                }
            }

            // 检查编码问题
            for (const field in encodingTest) {
                const encoding = encodingTest[field];
                if (encoding.byteLength !== encoding.length) {
                    issues.push(`${field}: 可能存在多字节字符编码问题`);
                }
            }

            return {
                totalIssues: issues.length,
                issues: issues,
                status: issues.length === 0 ? '✅ 未发现问题' : `⚠️ 发现${issues.length}个问题`
            };
        }

        /**
         * 基础功能测试函数
         * @function testBasicFunctionality - 测试基础功能可用性
         */
        window.testBasicFunctionality = function() {
            console.log('⚙️ 测试基础功能...');

            try {
                const results = {
                    escapeHtmlFunction: typeof escapeHtml === 'function',
                    collectFormDataFunction: typeof collectFormData === 'function',
                    updatePreviewFunction: typeof updatePreview === 'function',
                    debugContentConsistencyFunction: typeof debugContentConsistency === 'function',
                    modernExportSystemAvailable: typeof ModernExportSystem !== 'undefined',
                    html2canvasAvailable: typeof html2canvas !== 'undefined',
                    jsPDFAvailable: typeof window.jspdf !== 'undefined'
                };

                console.log('✅ 基础功能测试完成:', results);
                return results;

            } catch (error) {
                console.error('❌ 基础功能测试失败:', error);
                return { error: error.message };
            }
        };

        window.updatePreview = updatePreview;
        window.safeDebouncedUpdatePreview = safeDebouncedUpdatePreview;
        // window.exportToPDF 和 window.exportToImage 已移至 export-components.js 模块
        window.toggleAIFillPanel = toggleAIFillPanel;
        window.processAIFill = processAIFill;
        window.clearAIInput = clearAIInput;
        window.switchCurrency = switchCurrency;
        window.toggleCompanyFields = toggleCompanyFields;

        // 注意：updateExportMethodInfo函数已移除，因为简化的导出界面不再需要方法选择器

        // #endregion

        /**
         * 版本信息和开发说明
         * @description
         * 发票/收据生成器 v3.0 - 双语显示专业版
         *
         * 新增特性 v3.0:
         * - 🌐 双语并列显示模式（中文 / English）
         * - 🖼️ 图片占位区域显示（页眉、页脚、印章）
         * - 📋 发票字段增强（公司信息、税号等）
         * - 🧠 智能字段显示（空字段自动隐藏）
         * - 🔖 电子生成标识
         * - 🎨 Material Design风格优化
         * - 📄 A4单页布局优化
         * - 📱 响应式内容适配
         *
         * 核心特性:
         * - 独立HTML文件，支持file://协议
         * - 传统script标签架构，最大兼容性
         * - 内置图片资源管理（base64格式）
         * - 支持发票和收据两种文档类型
         * - 动态项目管理，自动计算金额
         * - PDF和图片导出功能
         * - 完整的中英双语界面
         *
         * 界面特点:
         * - 双语并列显示，无需切换
         * - Material Design设计规范
         * - 图片占位区域可视化
         * - 智能字段隐藏机制
         * - 电子生成水印标识
         *
         * 使用方法:
         * 1. 选择文档类型（发票/收据）
         * 2. 选择公司信息
         * 3. 填写公司详细信息（可选）
         * 4. 填写客户信息（支持实时预览）
         * 5. 添加项目明细（自动计算总金额）
         * 6. 实时查看预览效果
         * 7. 使用"导出PDF"或"导出图片"保存文档
         *
         * 响应式设计:
         * - 桌面端：双列布局，最佳编辑体验
         * - 平板端：优化间距，适中预览比例
         * - 移动端：单列布局，触摸友好界面
         * - 智能字体缩放，确保单页显示
         *
         * 图片资源:
         * - 可通过ImageBase64.updateImage()方法更新图片
         * - 支持logo、header、footer、stamp四种类型
         * - 图片格式为base64编码字符串
         * - 空图片显示占位区域
         *
         * 技术特点:
         * - 防抖机制避免频繁更新
         * - 智能字段显示逻辑
         * - 自适应布局，多设备兼容
         * - A4纸张严格控制
         * - 离线可用，无需网络连接
         */
    </script>
</body>
</html>
